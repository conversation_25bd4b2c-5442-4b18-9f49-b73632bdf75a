/**
 * 文件相关验证器模块
 * 专门处理文件路径、权限等文件相关的验证
 */

import type { SecurityEvent } from '../security/types.js';
import { getSecurityConfig } from '../security/config.js';
import {
  DANGEROUS_FILE_EXTENSIONS,
  WINDOWS_RESERVED_NAMES,
  DANGEROUS_PERMISSION_PATTERNS,
} from '../security/patterns.js';
import { StringValidator } from './string-validator.js';

/**
 * 文件验证器类
 * 提供企业级文件相关验证功能
 */
export class FileValidator {
  private static securityEventHandlers: Array<(event: SecurityEvent) => void> = [];

  /**
   * 添加安全事件处理器
   */
  static addSecurityEventHandler(handler: (event: SecurityEvent) => void): void {
    this.securityEventHandlers.push(handler);
  }

  /**
   * 触发安全事件
   */
  private static emitSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): void {
    const fullEvent: SecurityEvent = {
      ...event,
      timestamp: new Date(),
    };

    this.securityEventHandlers.forEach(handler => {
      try {
        handler(fullEvent);
      } catch (error) {
        console.error('Security event handler failed:', error);
      }
    });
  }

  /**
   * 企业级文件路径验证，防止多种路径遍历攻击
   */
  static validateFilePath(input: unknown, allowedExtensions?: string[]): string {
    const config = getSecurityConfig();

    // 基础字符串验证（不进行清理，保持原始路径用于安全检查）
    // 允许更多字符以便在后续安全检查中检测攻击
    const path = StringValidator.validateString(input, {
      maxLength: config.maxPathLength,
      pattern: /^[a-zA-Z0-9._/\\%:-]+$/,
      sanitize: false, // 不清理，保持原始路径
    });

    // 标准化路径（处理不同操作系统的路径分隔符）
    const normalizedPath = path.replace(/\\/g, '/');

    // 全面的路径遍历检查（增强检测）
    const DANGEROUS_PATTERNS = [
      /\.\./, // 基本的 ../
      // eslint-disable-next-line no-useless-escape
      /%2e%2e/gi, // URL编码的 ..
      // eslint-disable-next-line no-useless-escape
      /%252e%252e/gi, // 双重URL编码的 ..
      // eslint-disable-next-line no-useless-escape
      /%c0%ae%c0%ae/gi, // UTF-8编码绕过
      // eslint-disable-next-line no-useless-escape
      /\.\%2f/gi, // 混合编码
      // eslint-disable-next-line no-useless-escape
      /\%2e\./gi, // 混合编码
      // eslint-disable-next-line no-useless-escape
      /%252f/gi, // 双重编码的斜杠
      // eslint-disable-next-line no-useless-escape
      /%255c/gi, // 双重编码的反斜杠
      /\/\/+/, // 双斜杠或多斜杠
      /^\//, // 绝对路径
      /^[a-zA-Z]:/, // Windows驱动器路径
      /^\\\\/, // UNC路径
      /\0/, // 空字节
      // eslint-disable-next-line no-control-regex
      /[\x00-\x1f\x7f-\x9f]/, // 控制字符
      /[<>:"|?*]/, // Windows非法字符
      /\s$/, // 尾随空格
      /\.$/, // 尾随点
      /\.{2,}/, // 多个连续点
      // 长路径攻击防护
      /^.{260,}/, // Windows路径长度限制
    ];

    // 检查所有危险模式
    for (const pattern of DANGEROUS_PATTERNS) {
      if (pattern.test(normalizedPath)) {
        this.emitSecurityEvent({
          type: 'security_violation',
          message: 'Path traversal attack detected',
          input: normalizedPath,
          severity: 'high',
        });
        throw new Error('Path traversal attack detected');
      }
    }

    // 检查路径长度和深度
    const pathParts = normalizedPath.split('/').filter((part: string) => part.length > 0);
    if (pathParts.length > config.maxPathDepth) {
      throw new Error('File path too deep');
    }

    // 检查每个路径部分（增强版本）
    for (const part of pathParts) {
      if (part.length === 0 || part.length > config.maxPathComponentLength) {
        throw new Error('Invalid path component length');
      }

      // 检查路径部分的字符安全性
      if (!/^[a-zA-Z0-9._-]+$/.test(part)) {
        this.emitSecurityEvent({
          type: 'security_violation',
          message: `Invalid characters in path component: ${part}`,
          input: normalizedPath,
          severity: 'medium',
        });
        throw new Error('Invalid characters in path component');
      }

      // 检查保留名称（Windows）- 增强版本，检查带扩展名的保留名称
      const partWithoutExt = part.includes('.') ? part.substring(0, part.lastIndexOf('.')) : part;
      if (
        WINDOWS_RESERVED_NAMES.has(part.toUpperCase()) ||
        WINDOWS_RESERVED_NAMES.has(partWithoutExt.toUpperCase())
      ) {
        throw new Error('Reserved filename detected');
      }
    }

    // 文件扩展名验证
    const lastDotIndex = normalizedPath.lastIndexOf('.');

    // 安全的文件扩展名验证（如果指定了允许列表，优先检查）
    if (allowedExtensions && allowedExtensions.length > 0) {
      if (lastDotIndex === -1) {
        throw new Error('File must have an extension');
      }

      const ext = normalizedPath.substring(lastDotIndex + 1).toLowerCase();
      if (!ext || ext.length === 0 || ext.length > 10) {
        throw new Error('Invalid file extension');
      }

      // 检查扩展名是否在允许列表中
      const normalizedAllowedExts = allowedExtensions.map(e => e.toLowerCase());
      if (!normalizedAllowedExts.includes(ext)) {
        throw new Error(`File extension '${ext}' not allowed`);
      }
    } else {
      // 如果没有指定允许列表，检查危险的文件扩展名
      if (lastDotIndex !== -1) {
        const ext = normalizedPath.substring(lastDotIndex + 1).toLowerCase();
        if (ext && DANGEROUS_FILE_EXTENSIONS.has(ext)) {
          this.emitSecurityEvent({
            type: 'security_violation',
            message: `Dangerous file extension: ${ext}`,
            input: normalizedPath,
            severity: 'high',
          });
          throw new Error(`Dangerous file extension detected: ${ext}`);
        }
      }
    }

    return normalizedPath;
  }

  /**
   * 企业级权限字符串验证
   */
  static validatePermissions(input: unknown): string[] {
    const config = getSecurityConfig();

    // 处理字符串输入
    if (typeof input === 'string') {
      if (input.trim().length === 0) {
        return [];
      }

      // 限制权限字符串总长度
      if (input.length > config.maxPermissionsLength) {
        this.emitSecurityEvent({
          type: 'security_violation',
          message: `Permissions string too long: ${input.length}`,
          severity: 'medium',
        });
        throw new Error('Permissions string too long');
      }

      return input
        .split(',')
        .map(p => p.trim())
        .filter(p => p.length > 0)
        .map(p => this.validatePermission(p));
    }

    // 处理数组输入
    if (!Array.isArray(input)) {
      throw new Error('Input must be an array or string');
    }

    if (input.length > config.maxPermissionComponents) {
      throw new Error(`Too many permission components: ${input.length}`);
    }

    return input.map(p => this.validatePermission(p)).filter(p => p.length > 0);
  }

  /**
   * 验证单个权限字符串（增强安全性）
   */
  static validatePermission(permission: unknown): string {
    const config = getSecurityConfig();

    // 先进行基础字符串验证，但不限制长度
    const perm = StringValidator.validateString(permission, {
      maxLength: 1000, // 临时使用更大的长度限制
      pattern: /^[a-zA-Z0-9._*:-]+$/,
      sanitize: false, // 不清理权限字符串，保持原始格式
      allowEmpty: false,
    });

    // 检查权限字符串不能为空
    if (perm.length === 0) {
      throw new Error('Permission cannot be empty');
    }

    // 检查权限深度（防止过深的权限层级）- 优先检查
    const parts = perm.split('.');
    if (parts.length > 10) {
      throw new Error('Permission hierarchy too deep');
    }

    // 然后检查长度
    if (perm.length > config.maxPermissionComponentLength) {
      throw new Error('Permission component too long');
    }

    // 验证权限格式（更宽松的格式检查，支持更多合理的权限格式）
    const PERMISSION_PATTERN =
      /^[a-zA-Z][a-zA-Z0-9_-]*(?:\.[a-zA-Z0-9_*-]+)*(?:\*)?(?::[a-zA-Z0-9_-]+)?\.?$/;
    if (!PERMISSION_PATTERN.test(perm)) {
      throw new Error(`Invalid permission format: ${perm}`);
    }

    // 检查每个部分的长度
    for (const part of parts) {
      if (part.length > 50) {
        throw new Error('Permission component too long');
      }
    }

    // 检查危险的权限模式
    for (const dangerous of DANGEROUS_PERMISSION_PATTERNS) {
      if (perm.includes(dangerous)) {
        this.emitSecurityEvent({
          type: 'suspicious_input',
          message: `Potentially dangerous permission: ${perm}`,
          input: perm,
          severity: 'medium',
        });
        console.warn(`Potentially dangerous permission detected: ${perm}`);
      }
    }

    return perm;
  }
}
