/**
 * Markdown工具注册器 - 负责注册Markdown解析相关的工具
 * 包含：markdown_parse
 */

import type { ILogger } from '../../services/logger.service.js';
import type {
  IUnifiedToolRegistry,
  ToolDefinition,
  ToolHandler,
} from '../unified-tool-registry.js';

export interface IMarkdownToolsRegistrar {
  registerMarkdownTools(): void;
}

export class MarkdownToolsRegistrar implements IMarkdownToolsRegistrar {
  private readonly REGISTRAR_NAME = 'MarkdownTools';

  constructor(
    private unifiedRegistry: IUnifiedToolRegistry,
    private logger: ILogger
  ) {}

  /**
   * 注册所有Markdown工具
   */
  registerMarkdownTools(): void {
    try {
      // 获取所有Markdown工具定义
      const toolDefinitions = this.getMarkdownToolDefinitions();

      // 创建工具处理器
      const toolHandler = this.createToolHandler();

      // 注册所有工具到统一注册表
      for (const toolDefinition of toolDefinitions) {
        this.unifiedRegistry.registerTool(toolDefinition, toolHandler, this.REGISTRAR_NAME);
        this.logger.debug(`Registered Markdown tool: ${toolDefinition.name}`);
      }

      this.logger.info(`Markdown tools registered successfully. Total: ${toolDefinitions.length}`);
    } catch (error) {
      this.logger.error('Failed to register Markdown tools', error as Error);
      throw new Error(
        `Markdown tools registration failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * 获取所有Markdown工具定义
   */
  private getMarkdownToolDefinitions(): ToolDefinition[] {
    return [
      {
        name: 'markdown_parse',
        description: '解析 Markdown 内容并提取结构化信息',
        inputSchema: {
          type: 'object',
          properties: {
            content: {
              type: 'string',
              description: 'Markdown 内容',
            },
            extractParameters: {
              type: 'boolean',
              description: '是否提取参数定义',
              default: true,
            },
            parseAsPrompt: {
              type: 'boolean',
              description: '是否解析为提示模板格式',
              default: true,
            },
          },
          required: ['content'],
          additionalProperties: false,
        },
      },
    ];
  }

  /**
   * 创建工具处理器
   */
  private createToolHandler(): ToolHandler {
    return async (name: string, args: Record<string, unknown>) => {
      return this.handleToolCall(name, args);
    };
  }

  /**
   * 处理工具调用
   */
  private async handleToolCall(
    name: string,
    args: Record<string, unknown>
  ): Promise<{
    content: Array<{
      type: 'text';
      text: string;
    }>;
    isError?: boolean;
  }> {
    try {
      switch (name) {
        case 'markdown_parse':
          return this.handleMarkdownParse(args);

        default:
          throw new Error(`Unknown Markdown tool: ${name}`);
      }
    } catch (error) {
      this.logger.error(`Markdown tool call failed for ${name}`, error as Error);
      throw error;
    }
  }

  /**
   * 处理Markdown解析工具调用 - 完整实现
   */
  private async handleMarkdownParse(args: Record<string, unknown>): Promise<{
    content: Array<{
      type: 'text';
      text: string;
    }>;
  }> {
    const {
      content,
      extractParameters = true,
      parseAsPrompt = true,
    } = args as {
      content: string;
      extractParameters?: boolean;
      parseAsPrompt?: boolean;
    };

    if (!content || typeof content !== 'string') {
      throw new Error('Content parameter is required and must be a string');
    }

    try {
      let result: unknown;

      if (parseAsPrompt) {
        // 导入 Markdown 解析器
        const { MarkdownPromptParser } = await import('../../utils/markdown-parser.js');

        // 使用静态方法解析 Markdown 为提示模板
        const promptTemplate = MarkdownPromptParser.parseMarkdownPrompt(content, 'inline-content');

        result = {
          success: true,
          format: 'prompt-template',
          name: promptTemplate.name,
          description: promptTemplate.description,
          arguments: promptTemplate.arguments || [],
          messages: promptTemplate.messages.map(msg => ({
            role: msg.role,
            content:
              msg.content.text?.substring(0, 1000) +
              (msg.content.text && msg.content.text.length > 1000 ? '...' : ''),
          })),
          metadata: {
            format: 'markdown',
            originalLength: content.length,
            messageCount: promptTemplate.messages.length,
            hasArguments: !!(promptTemplate.arguments && promptTemplate.arguments.length > 0),
          },
          timestamp: new Date().toISOString(),
        };
      } else {
        // 基础Markdown解析
        const basicResult = this.parseBasicMarkdown(content, extractParameters);
        result = {
          success: true,
          format: 'basic-markdown',
          ...basicResult,
          timestamp: new Date().toISOString(),
        };
      }

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      this.logger.error('Markdown parsing failed', error as Error, {
        contentLength: content.length,
        parseAsPrompt,
        extractParameters,
      });
      throw new Error(
        `Markdown parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * 基础Markdown解析
   */
  private parseBasicMarkdown(content: string, extractParameters: boolean): Record<string, unknown> {
    const result: Record<string, unknown> = {
      content,
      wordCount: content.split(/\s+/).filter(word => word.length > 0).length,
      lineCount: content.split('\n').length,
      characterCount: content.length,
    };

    // 提取标题
    const headers = content.match(/^#{1,6}\s+.+$/gm) || [];
    result['headers'] = headers.map(h => ({
      level: h.match(/^#+/)?.[0].length || 0,
      text: h.replace(/^#+\s+/, '').trim(),
    }));

    // 提取链接
    const links = content.match(/\[([^\]]+)\]\(([^)]+)\)/g) || [];
    result['links'] = links
      .map(link => {
        const match = link.match(/\[([^\]]+)\]\(([^)]+)\)/);
        return match ? { text: match[1], url: match[2] } : null;
      })
      .filter(Boolean);

    // 提取代码块
    const codeBlocks = content.match(/```[\s\S]*?```/g) || [];
    result['codeBlocks'] = codeBlocks.map(block => {
      const lines = block.split('\n');
      const language = lines.length > 0 ? lines[0]!.replace('```', '').trim() : '';
      const code = lines.slice(1, -1).join('\n');
      return { language, code, lineCount: lines.length - 2 };
    });

    // 提取内联代码
    const inlineCode = content.match(/`([^`]+)`/g) || [];
    result['inlineCode'] = inlineCode.map(code => code.replace(/`/g, ''));

    // 提取列表项
    const listItems = content.match(/^[\s]*[-*+]\s+.+$/gm) || [];
    result['listItems'] = listItems.map(item => item.replace(/^[\s]*[-*+]\s+/, '').trim());

    // 提取引用
    const blockquotes = content.match(/^>\s+.+$/gm) || [];
    result['blockquotes'] = blockquotes.map(quote => quote.replace(/^>\s+/, '').trim());

    // 提取表格
    const tables = this.extractTables(content);
    result['tables'] = tables;

    // 如果需要提取参数
    if (extractParameters) {
      result['parameters'] = this.extractParameters(content);
    }

    // 统计信息
    result['statistics'] = {
      headerCount: headers.length,
      linkCount: links.length,
      codeBlockCount: codeBlocks.length,
      inlineCodeCount: inlineCode.length,
      listItemCount: listItems.length,
      blockquoteCount: blockquotes.length,
      tableCount: tables.length,
    };

    return result;
  }

  /**
   * 提取表格
   */
  private extractTables(content: string): Array<{ headers: string[]; rows: string[][] }> {
    const tables: Array<{ headers: string[]; rows: string[][] }> = [];
    const lines = content.split('\n');

    for (let i = 0; i < lines.length - 1; i++) {
      const line = lines[i]?.trim() || '';
      const nextLine = lines[i + 1]?.trim() || '';

      // 检查是否是表格头部
      if (line.includes('|') && nextLine.match(/^\|?[\s]*:?-+:?[\s]*\|/)) {
        const headers = line
          .split('|')
          .map(h => h.trim())
          .filter(h => h.length > 0);
        const rows: string[][] = [];

        // 跳过分隔行，开始读取数据行
        i += 2;
        while (i < lines.length && (lines[i]?.trim() || '').includes('|')) {
          const currentLine = lines[i]?.trim() || '';
          const row = currentLine
            .split('|')
            .map(cell => cell.trim())
            .filter(cell => cell.length > 0);
          if (row.length > 0) {
            rows.push(row);
          }
          i++;
        }

        tables.push({ headers, rows });
        i--; // 回退一行，因为外层循环会自增
      }
    }

    return tables;
  }

  /**
   * 提取参数定义
   */
  private extractParameters(
    content: string
  ): Array<{ name: string; description?: string; required?: boolean }> {
    const parameters: Array<{ name: string; description?: string; required?: boolean }> = [];

    // 查找参数定义模式：{paramName} 或 {{paramName}}
    const paramMatches = content.match(/\{+([^}]+)\}+/g) || [];
    const uniqueParams = new Set<string>();

    for (const match of paramMatches) {
      const paramName = match.replace(/[{}]/g, '');
      if (!uniqueParams.has(paramName)) {
        uniqueParams.add(paramName);

        // 尝试找到参数描述（在参数前后的注释或说明）
        const paramRegex = new RegExp(
          `\\{+${paramName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\}+`,
          'g'
        );
        const paramIndex = content.search(paramRegex);

        let description = '';
        let required = false;

        if (paramIndex !== -1) {
          // 查找前后文中的描述
          const beforeText = content.substring(Math.max(0, paramIndex - 100), paramIndex);
          const afterText = content.substring(
            paramIndex,
            Math.min(content.length, paramIndex + 100)
          );

          // 简单的描述提取逻辑
          const descMatch =
            beforeText.match(/<!--\s*([^-]+)\s*-->/) || afterText.match(/<!--\s*([^-]+)\s*-->/);
          if (descMatch && descMatch[1]) {
            description = descMatch[1].trim();
          }

          // 检查是否标记为必需
          required =
            beforeText.includes('required') ||
            afterText.includes('required') ||
            match.startsWith('{{');
        }

        const param: { name: string; description?: string; required?: boolean } = {
          name: paramName,
        };

        if (description) {
          param.description = description;
        }

        if (required) {
          param.required = required;
        }

        parameters.push(param);
      }
    }

    return parameters;
  }
}
