/**
 * 配置管理服务 - 提供统一的配置管理接口
 * 重构为模块化设计，符合500行限制
 */

import { EventEmitter } from 'eventemitter3';
import type { ServerConfig } from '../types/config.js';
import { ConfigLoader, type IConfigLoader } from './config-loader.service.js';
import { ConfigWatcher, type IConfigWatcher } from './config-watcher.service.js';

export interface IConfigService {
  get<T = unknown>(path: string): T;
  set(path: string, value: unknown): void;
  getAll(): ServerConfig;
  watch(path: string, callback: (value: unknown) => void): void;
  reload(): Promise<void>;
}

export class ConfigService extends EventEmitter implements IConfigService {
  private config: ServerConfig;
  private configPath: string;
  private configLoader: IConfigLoader;
  private configWatcher: IConfigWatcher;

  constructor(configPath?: string) {
    super();
    this.configLoader = new ConfigLoader();
    this.configWatcher = new ConfigWatcher();

    this.configPath = configPath || this.configLoader.findConfigFile();
    this.config = this.configLoader.loadConfig(this.configPath);
    this.setupWatchers();
  }

  /**
   * 获取配置值
   */
  get<T = unknown>(path: string): T {
    return this.getNestedValue(this.config as unknown as Record<string, unknown>, path) as T;
  }

  /**
   * 设置配置值
   */
  set(path: string, value: unknown): void {
    this.setNestedValue(this.config as unknown as Record<string, unknown>, path, value);
    this.emit('configChanged', path, value);
  }

  /**
   * 获取所有配置
   */
  getAll(): ServerConfig {
    return { ...this.config };
  }

  /**
   * 监听配置变化
   */
  watch(path: string, callback: (value: unknown) => void): void {
    this.configWatcher.watch(this.configPath, callback);
    this.on(`config:${path}`, callback);
  }

  /**
   * 重新加载配置
   */
  async reload(): Promise<void> {
    try {
      const newConfig = this.configLoader.loadConfig(this.configPath);
      const oldConfig = this.config;
      this.config = newConfig;

      this.emit('configReloaded', { oldConfig, newConfig });
      // Configuration reloaded successfully
    } catch (error) {
      console.error('Failed to reload configuration:', error);
      throw error;
    }
  }

  /**
   * 设置配置文件监听器
   */
  private setupWatchers(): void {
    this.configWatcher.setupWatchers(this.configPath);

    // 监听配置文件变化
    this.configWatcher.on('configChanged', async () => {
      try {
        await this.reload();
      } catch (error) {
        console.error('Failed to reload configuration after file change:', error);
      }
    });
  }

  /**
   * 获取嵌套配置值
   */
  private getNestedValue(obj: Record<string, unknown>, path: string): unknown {
    const keys = path.split('.');
    let current = obj;

    for (const key of keys) {
      if (current === null || current === undefined || typeof current !== 'object') {
        return undefined;
      }
      current = current[key] as Record<string, unknown>;
    }

    return current;
  }

  /**
   * 设置嵌套配置值
   */
  private setNestedValue(obj: Record<string, unknown>, path: string, value: unknown): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    let current = obj;

    for (const key of keys) {
      if (!(key in current) || typeof current[key] !== 'object' || current[key] === null) {
        current[key] = {};
      }
      current = current[key] as Record<string, unknown>;
    }

    current[lastKey] = value;
  }

  /**
   * 销毁配置服务
   */
  destroy(): void {
    this.configWatcher.destroy();
    this.removeAllListeners();
    // ConfigService destroyed
  }

  /**
   * 获取配置文件路径
   */
  getConfigPath(): string {
    return this.configPath;
  }

  /**
   * 检查配置是否有效
   */
  isValid(): boolean {
    try {
      this.configLoader.validateConfig(this.config);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取配置统计信息
   */
  getStats(): {
    configPath: string;
    isValid: boolean;
    watchersCount: number;
    listenersCount: number;
  } {
    return {
      configPath: this.configPath,
      isValid: this.isValid(),
      watchersCount: this.configWatcher.getActiveWatchersCount(),
      listenersCount: this.listenerCount('configChanged'),
    };
  }
}
