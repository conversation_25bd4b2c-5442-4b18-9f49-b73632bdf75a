/**
 * 配置加载器服务 - 负责配置文件的查找、加载和验证
 */

import { existsSync, readFileSync } from 'fs';
import path from 'path';
import type { ServerConfig } from '../types/config.js';

export interface IConfigLoader {
  findConfigFile(): string;
  loadConfig(configPath?: string): ServerConfig;
  validateConfig(config: unknown): ServerConfig;
  getDefaultConfig(): ServerConfig;
}

export class ConfigLoader implements IConfigLoader {
  /**
   * 查找配置文件
   */
  findConfigFile(): string {
    const env = process.env['NODE_ENV'] || 'development';

    const possiblePaths = [
      `./config/${env}.json`,
      './mcp-server.config.js',
      './mcp-server.config.json',
      './config/default.json',
      './config.json',
    ];

    for (const configPath of possiblePaths) {
      if (existsSync(configPath)) {
        return configPath;
      }
    }

    // 如果没有找到配置文件，返回默认配置路径
    return './config/default.json';
  }

  /**
   * 加载配置文件
   */
  loadConfig(configPath?: string): ServerConfig {
    const actualConfigPath = configPath || this.findConfigFile();

    try {
      if (!existsSync(actualConfigPath)) {
        console.warn(
          `Configuration file not found: ${actualConfigPath}, using default configuration`
        );
        return this.getDefaultConfig();
      }

      const configContent = readFileSync(actualConfigPath, 'utf-8');
      let rawConfig: unknown;

      // 根据文件扩展名解析配置
      if (actualConfigPath.endsWith('.js')) {
        // 动态导入 JavaScript 配置文件
        delete require.cache[path.resolve(actualConfigPath)];
        rawConfig = require(path.resolve(actualConfigPath));
      } else {
        // 解析 JSON 配置文件
        rawConfig = JSON.parse(configContent);
      }

      return this.validateConfig(rawConfig);
    } catch (error) {
      console.error(`Failed to load configuration from ${actualConfigPath}:`, error);
      console.warn('Using default configuration');
      return this.getDefaultConfig();
    }
  }

  /**
   * 验证配置对象
   */
  validateConfig(config: unknown): ServerConfig {
    try {
      // 简单的类型检查，避免严格的 schema 验证
      if (config && typeof config === 'object' && config !== null) {
        return config as ServerConfig;
      }
      throw new Error('Invalid configuration object');
    } catch (error) {
      console.error('Configuration validation failed:', error);
      console.warn('Using default configuration');
      return this.getDefaultConfig();
    }
  }

  /**
   * 获取默认配置
   */
  getDefaultConfig(): ServerConfig {
    return {
      server: {
        name: process.env['MCP_SERVER_NAME'] || 'mcp-prompt-server',
        version: process.env['MCP_SERVER_VERSION'] || '1.0.0',
        ...(process.env['MCP_SERVER_DESCRIPTION'] && {
          description: process.env['MCP_SERVER_DESCRIPTION'],
        }),
        ...(process.env['MCP_SERVER_PORT'] && {
          port: parseInt(process.env['MCP_SERVER_PORT'], 10),
        }),
        ...(process.env['MCP_SERVER_PROMPTS_AS_TOOLS'] && {
          promptsAsTools: process.env['MCP_SERVER_PROMPTS_AS_TOOLS'] === 'true',
        }),
      },
      prompts: {
        directories: [process.env['MCP_PROMPTS_DIRECTORY'] || './prompts'],
        watchForChanges: process.env['MCP_PROMPTS_WATCH'] === 'true',
        cacheEnabled: process.env['MCP_PROMPTS_CACHE'] !== 'false',
        supportedFormats: ['.md', '.txt', '.json'],
        maxFileSize: parseInt(process.env['MCP_PROMPTS_MAX_FILE_SIZE'] || '1048576', 10), // 1MB
      },

      logging: {
        level: (process.env['MCP_LOG_LEVEL'] as 'debug' | 'info' | 'warn' | 'error') || 'info',
        format: (process.env['MCP_LOG_FORMAT'] as 'json' | 'text') || 'text',
        ...(process.env['MCP_LOG_FILE'] && { file: process.env['MCP_LOG_FILE'] }),
        console: process.env['MCP_LOG_CONSOLE'] !== 'false',
      },
      plugins: {
        enabled: process.env['MCP_PLUGINS_ENABLED']?.split(',') || [],
        config: {},
        autoLoad: process.env['MCP_PLUGINS_AUTO_LOAD'] !== 'false',
      },
      performance: {
        maxConcurrentTools: parseInt(process.env['MCP_PERFORMANCE_MAX_CONCURRENT'] || '10', 10),
        requestTimeout: parseInt(process.env['MCP_PERFORMANCE_TIMEOUT'] || '30000', 10),
        cacheSize: parseInt(process.env['MCP_PERFORMANCE_CACHE_SIZE'] || '100', 10),
        maxExecutionTime: parseInt(
          process.env['MCP_PERFORMANCE_MAX_EXECUTION_TIME'] || '60000',
          10
        ),
        enableMonitoring: process.env['MCP_PERFORMANCE_MONITORING'] !== 'false',
        memoryThreshold: parseInt(
          process.env['MCP_PERFORMANCE_MEMORY_THRESHOLD'] || '536870912',
          10
        ), // 512MB
      },
      security: {
        strictMode: process.env['MCP_SECURITY_STRICT_MODE'] === 'true',
        maxStringLength: parseInt(process.env['MCP_SECURITY_MAX_STRING_LENGTH'] || '10000', 10),
        maxArrayLength: parseInt(process.env['MCP_SECURITY_MAX_ARRAY_LENGTH'] || '1000', 10),
        maxExpressionLength: parseInt(
          process.env['MCP_SECURITY_MAX_EXPRESSION_LENGTH'] || '1000',
          10
        ),
        blockSuspiciousInput: process.env['MCP_SECURITY_BLOCK_SUSPICIOUS'] !== 'false',
        enableRateLimiting: process.env['MCP_SECURITY_RATE_LIMIT'] !== 'false',
        maxRequestsPerMinute: parseInt(
          process.env['MCP_SECURITY_MAX_REQUESTS_PER_MINUTE'] || '100',
          10
        ),
      },
    };
  }
}
