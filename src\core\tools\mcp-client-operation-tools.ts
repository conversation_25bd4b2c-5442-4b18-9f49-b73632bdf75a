/**
 * MCP客户端操作工具注册器 - 负责注册MCP客户端操作相关的工具
 * 包含：mcp_client_call_tool, mcp_client_get_tools
 */

import type { ILogger } from '../../services/logger.service.js';
import type { IMCPClientManager } from '../../services/mcp-client-manager.service.js';
import type {
  IUnifiedToolRegistry,
  ToolDefinition,
  ToolHandler,
} from '../unified-tool-registry.js';

export interface IMCPClientOperationToolsRegistrar {
  registerMCPClientOperationTools(): void;
}

export class MCPClientOperationToolsRegistrar implements IMCPClientOperationToolsRegistrar {
  private readonly REGISTRAR_NAME = 'MCPClientOperationTools';

  constructor(
    private unifiedRegistry: IUnifiedToolRegistry,
    private logger: ILogger,
    private mcpClientManager: IMCPClientManager
  ) {}

  /**
   * 注册所有MCP客户端操作工具
   */
  registerMCPClientOperationTools(): void {
    try {
      // 获取所有MCP客户端操作工具定义
      const toolDefinitions = this.getMCPClientOperationToolDefinitions();

      // 创建工具处理器
      const toolHandler = this.createToolHandler();

      // 注册所有工具到统一注册表
      for (const toolDefinition of toolDefinitions) {
        this.unifiedRegistry.registerTool(toolDefinition, toolHandler, this.REGISTRAR_NAME);
        this.logger.debug(`Registered MCP Client Operation tool: ${toolDefinition.name}`);
      }

      this.logger.info(
        `MCP Client Operation tools registered successfully. Total: ${toolDefinitions.length}`
      );
    } catch (error) {
      this.logger.error('Failed to register MCP Client Operation tools', error as Error);
      throw new Error(
        `MCP Client Operation tools registration failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * 获取所有MCP客户端操作工具定义
   */
  private getMCPClientOperationToolDefinitions(): ToolDefinition[] {
    return [
      {
        name: 'mcp_client_call_tool',
        description: '调用指定MCP服务器的工具',
        inputSchema: {
          type: 'object',
          properties: {
            serverName: {
              type: 'string',
              description: 'MCP服务器名称',
              default: 'default',
            },
            toolName: {
              type: 'string',
              description: '要调用的工具名称',
            },
            arguments: {
              type: 'object',
              description: '工具参数',
              default: {},
            },
          },
          required: ['toolName'],
          additionalProperties: false,
        },
      },
      {
        name: 'mcp_client_get_tools',
        description: '获取指定MCP服务器的工具列表',
        inputSchema: {
          type: 'object',
          properties: {
            serverName: {
              type: 'string',
              description: 'MCP服务器名称',
              default: 'default',
            },
          },
          additionalProperties: false,
        },
      },
    ];
  }

  /**
   * 创建工具处理器
   */
  private createToolHandler(): ToolHandler {
    return async (name: string, args: Record<string, unknown>) => {
      return this.handleToolCall(name, args);
    };
  }

  /**
   * 处理工具调用 - 完整实现，不简化
   */
  private async handleToolCall(
    name: string,
    args: Record<string, unknown>
  ): Promise<{
    content: Array<{
      type: 'text';
      text: string;
    }>;
    isError?: boolean;
  }> {
    try {
      switch (name) {
        case 'mcp_client_call_tool':
          return this.handleMCPClientCallTool(args);

        case 'mcp_client_get_tools':
          return this.handleMCPClientGetTools(args);

        default:
          throw new Error(`Unknown MCP Client Operation tool: ${name}`);
      }
    } catch (error) {
      this.logger.error(`MCP Client Operation tool call failed for ${name}`, error as Error);
      throw error;
    }
  }

  /**
   * 处理MCP客户端调用工具的工具调用 - 完整实现
   */
  private async handleMCPClientCallTool(args: Record<string, unknown>): Promise<{
    content: Array<{
      type: 'text';
      text: string;
    }>;
  }> {
    const {
      serverName = 'default',
      toolName,
      arguments: toolArgs = {},
    } = args as {
      serverName?: string;
      toolName: string;
      arguments?: Record<string, unknown>;
    };

    if (!toolName) {
      throw new Error('toolName parameter is required');
    }

    try {
      const result = await this.mcpClientManager.callTool(serverName, toolName, toolArgs);

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      this.logger.error('Failed to call MCP tool via tool', error as Error, {
        serverName,
        toolName,
        toolArgs,
      });
      throw new Error(
        `Failed to call MCP tool: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * 处理获取MCP客户端工具列表的工具调用 - 完整实现
   */
  private async handleMCPClientGetTools(args: Record<string, unknown>): Promise<{
    content: Array<{
      type: 'text';
      text: string;
    }>;
  }> {
    const { serverName = 'default' } = args as {
      serverName?: string;
    };

    try {
      const result = this.mcpClientManager.getServerTools(serverName);

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      this.logger.error('Failed to get MCP tools via tool', error as Error, {
        serverName,
      });
      throw new Error(
        `Failed to get MCP tools: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }
}
