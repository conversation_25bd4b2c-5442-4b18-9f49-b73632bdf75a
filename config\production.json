{"server": {"name": "mcp-prompt-server", "version": "2.0.0", "description": "Production MCP Prompt Server with Enterprise Features", "promptsAsTools": true}, "prompts": {"directories": ["./dist/prompts", "./dist/custom-prompts"], "watchForChanges": false, "cacheEnabled": true, "supportedFormats": ["yaml", "json", "js", "ts", "md"], "maxFileSize": 1048576}, "logging": {"level": "info", "format": "json", "file": "./logs/production.log", "console": false}, "plugins": {"enabled": ["validation", "cache", "metrics", "authentication", "health-check"], "config": {"validation": {"strict": true, "allowExtraArgs": false}, "cache": {"ttl": 3600, "maxSize": 10000, "compression": true}, "metrics": {"enabled": true, "interval": 30000, "exportPath": "./metrics", "retention": "7d"}, "authentication": {"enabled": true, "tokenExpiration": 86400000, "maxAttempts": 5, "lockoutDuration": 900000}, "health-check": {"enabled": true, "endpoint": "/health", "interval": 30000, "checks": ["memory", "disk", "prompts", "config", "authentication"]}}, "autoLoad": true}, "security": {"strictMode": true, "maxStringLength": 10000, "maxArrayLength": 1000, "maxExpressionLength": 1000, "blockSuspiciousInput": true, "enableRateLimiting": true, "maxRequestsPerMinute": 1000}, "performance": {"maxConcurrentTools": 50, "requestTimeout": 30000, "cacheSize": 10000, "maxExecutionTime": 5000, "enableMonitoring": true, "memoryThreshold": 50000000}}