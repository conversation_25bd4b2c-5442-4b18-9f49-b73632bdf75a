/**
 * MCP客户端连接工具注册器 - 负责注册MCP客户端连接管理相关的工具
 * 包含：mcp_client_load_config, mcp_client_connect, mcp_client_list_connections
 */

import type { ILogger } from '../../services/logger.service.js';
import type { IMCPClientManager } from '../../services/mcp-client-manager.service.js';
import type {
  IUnifiedToolRegistry,
  ToolDefinition,
  ToolHandler,
} from '../unified-tool-registry.js';

export interface IMCPClientConnectionToolsRegistrar {
  registerMCPClientConnectionTools(): void;
}

export class MCPClientConnectionToolsRegistrar implements IMCPClientConnectionToolsRegistrar {
  private readonly REGISTRAR_NAME = 'MCPClientConnectionTools';

  constructor(
    private unifiedRegistry: IUnifiedToolRegistry,
    private logger: ILogger,
    private mcpClientManager: IMCPClientManager
  ) {}

  /**
   * 注册所有MCP客户端连接工具
   */
  registerMCPClientConnectionTools(): void {
    try {
      // 获取所有MCP客户端连接工具定义
      const toolDefinitions = this.getMCPClientConnectionToolDefinitions();

      // 创建工具处理器
      const toolHandler = this.createToolHandler();

      // 注册所有工具到统一注册表
      for (const toolDefinition of toolDefinitions) {
        this.unifiedRegistry.registerTool(toolDefinition, toolHandler, this.REGISTRAR_NAME);
        this.logger.debug(`Registered MCP Client Connection tool: ${toolDefinition.name}`);
      }

      this.logger.info(
        `MCP Client Connection tools registered successfully. Total: ${toolDefinitions.length}`
      );
    } catch (error) {
      this.logger.error('Failed to register MCP Client Connection tools', error as Error);
      throw new Error(
        `MCP Client Connection tools registration failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * 获取所有MCP客户端连接工具定义
   */
  private getMCPClientConnectionToolDefinitions(): ToolDefinition[] {
    return [
      {
        name: 'mcp_client_load_config',
        description: '加载MCP客户端配置文件',
        inputSchema: {
          type: 'object',
          properties: {},
          additionalProperties: false,
        },
      },
      {
        name: 'mcp_client_connect',
        description: '连接到指定的MCP服务器',
        inputSchema: {
          type: 'object',
          properties: {},
          additionalProperties: false,
        },
      },
      {
        name: 'mcp_client_list_connections',
        description: '列出所有MCP服务器连接状态',
        inputSchema: {
          type: 'object',
          properties: {},
          additionalProperties: false,
        },
      },
    ];
  }

  /**
   * 创建工具处理器
   */
  private createToolHandler(): ToolHandler {
    return async (name: string, args: Record<string, unknown>) => {
      return this.handleToolCall(name, args);
    };
  }

  /**
   * 处理工具调用 - 完整实现，不简化
   */
  private async handleToolCall(
    name: string,
    _args: Record<string, unknown>
  ): Promise<{
    content: Array<{
      type: 'text';
      text: string;
    }>;
    isError?: boolean;
  }> {
    try {
      switch (name) {
        case 'mcp_client_load_config':
          return this.handleMCPClientLoadConfig();

        case 'mcp_client_connect':
          return this.handleMCPClientConnect();

        case 'mcp_client_list_connections':
          return this.handleMCPClientListConnections();

        default:
          throw new Error(`Unknown MCP Client Connection tool: ${name}`);
      }
    } catch (error) {
      this.logger.error(`MCP Client Connection tool call failed for ${name}`, error as Error);
      throw error;
    }
  }

  /**
   * 处理MCP客户端加载配置工具调用 - 完整实现
   */
  private async handleMCPClientLoadConfig(): Promise<{
    content: Array<{
      type: 'text';
      text: string;
    }>;
  }> {
    try {
      const result = await this.mcpClientManager.loadConfig('./mcp/mcp.json');

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      this.logger.error('Failed to load MCP client config via tool', error as Error);
      throw new Error(
        `Failed to load MCP client config: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * 处理MCP客户端连接工具调用 - 完整实现
   */
  private async handleMCPClientConnect(): Promise<{
    content: Array<{
      type: 'text';
      text: string;
    }>;
  }> {
    try {
      const result = await this.mcpClientManager.connectServer('default');

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      this.logger.error('Failed to connect to MCP server via tool', error as Error);
      throw new Error(
        `Failed to connect to MCP server: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * 处理MCP客户端连接列表工具调用 - 完整实现
   */
  private async handleMCPClientListConnections(): Promise<{
    content: Array<{
      type: 'text';
      text: string;
    }>;
  }> {
    try {
      const result = await this.mcpClientManager.listConnections();

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      this.logger.error('Failed to list MCP connections via tool', error as Error);
      throw new Error(
        `Failed to list MCP connections: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }
}
