# 启动脚本说明

本目录包含了 MCP Prompt Server 的跨平台启动脚本，确保在不同操作系统和环境下都能正确运行。

## 脚本文件

### 1. `start.js` - 通用 Node.js 启动脚本
- **用途**: 跨平台的主启动脚本
- **特点**: 自动检测环境、设置路径、处理信号
- **推荐**: 所有环境的首选启动方式

### 2. `start.bat` - Windows 批处理脚本
- **用途**: Windows 系统专用
- **特点**: 原生 Windows 命令行支持
- **适用**: Windows 用户直接双击运行

### 3. `start.sh` - Unix/Linux Shell 脚本
- **用途**: Unix/Linux/macOS 系统
- **特点**: 彩色输出、信号处理、错误检查
- **适用**: 服务器部署和开发环境

### 4. `copy-assets.js` - 资源复制脚本
- **用途**: 构建时复制静态资源
- **特点**: 自动复制配置、模板等文件
- **调用**: 由 `npm run build` 自动执行

## 使用方法

### 通过 npm scripts（推荐）

```bash
# 使用智能启动脚本（推荐）
npm start

# 直接启动（需要先构建）
npm run start:direct

# Windows 专用启动
npm run start:windows

# Unix/Linux 专用启动
npm run start:unix

# 开发模式（监听文件变化）
npm run dev

# 完整设置（安装依赖 + 构建）
npm run setup
```

### 直接运行脚本

```bash
# 通用启动脚本
node scripts/start.js

# Windows 批处理
scripts\start.bat

# Unix/Linux Shell
bash scripts/start.sh
```

## 环境变量配置

所有启动脚本都支持以下环境变量：

### 基础配置
- `NODE_ENV`: 运行环境 (development/production)
- `PROJECT_ROOT`: 项目根目录路径

### Prompt 配置
- `MCP_PROMPTS_DIRECTORIES`: Prompt 目录列表（逗号分隔）
- `MCP_PROMPTS_WATCH`: 是否监听文件变化
- `MCP_PROMPTS_CACHE`: 是否启用缓存

### 日志配置
- `MCP_LOG_LEVEL`: 日志级别 (debug/info/warn/error)
- `MCP_LOG_FORMAT`: 日志格式 (text/json)
- `MCP_LOG_FILE`: 日志文件路径

### 安全配置
- `SECURITY_STRICT_MODE`: 严格安全模式 (true/false)

## 自动检测功能

### 1. 环境检测
- 自动检测开发/生产环境
- 根据环境设置合适的默认配置

### 2. 路径检测
- 自动查找项目根目录
- 智能解析相对路径
- 跨平台路径兼容

### 3. 脚本检测
- 优先使用编译后的 JS 文件
- 开发环境自动使用 TypeScript
- 自动选择合适的运行器

## 使用示例

### 开发环境启动
```bash
# 设置开发环境
export NODE_ENV=development
export MCP_LOG_LEVEL=debug

# 启动服务器
npm start
```

### 生产环境启动
```bash
# 设置生产环境
export NODE_ENV=production
export MCP_LOG_LEVEL=info
export MCP_LOG_FORMAT=json
export SECURITY_STRICT_MODE=true

# 启动服务器
npm start
```

### 自定义 Prompt 目录
```bash
# 设置自定义目录
export MCP_PROMPTS_DIRECTORIES="./my-prompts,./shared-prompts"

# 启动服务器
npm start
```

### Docker 环境
```dockerfile
# Dockerfile 示例
FROM node:18-alpine

WORKDIR /app
COPY . .

# 设置环境变量
ENV NODE_ENV=production
ENV PROJECT_ROOT=/app
ENV MCP_PROMPTS_DIRECTORIES=/app/prompts,/app/custom-prompts

# 安装依赖并构建
RUN npm run setup

# 启动服务器
CMD ["npm", "start"]
```

## 故障排除

### 1. 找不到主脚本
```
❌ Error: Cannot find main script
```
**解决方案**: 运行 `npm run build` 构建项目

### 2. 权限错误（Unix/Linux）
```bash
# 设置执行权限
chmod +x scripts/start.sh
```

### 3. 路径问题
- 确保使用相对路径
- 检查 `PROJECT_ROOT` 环境变量
- 验证 Prompt 目录是否存在

### 4. 端口占用
- 检查是否有其他进程占用端口
- 使用不同的端口配置

## 最佳实践

1. **使用 npm scripts**: 推荐使用 `npm start` 而不是直接运行脚本
2. **环境隔离**: 不同环境使用不同的环境变量配置
3. **路径配置**: 避免硬编码绝对路径
4. **日志配置**: 生产环境使用 JSON 格式日志
5. **安全配置**: 生产环境启用严格安全模式
6. **监控**: 使用进程管理器（如 PM2）进行生产部署

## 进程管理

### 使用 PM2（推荐生产环境）
```bash
# 安装 PM2
npm install -g pm2

# 启动服务器
pm2 start scripts/start.js --name mcp-prompt-server

# 查看状态
pm2 status

# 查看日志
pm2 logs mcp-prompt-server

# 重启服务器
pm2 restart mcp-prompt-server
```

### 使用 systemd（Linux）
```ini
# /etc/systemd/system/mcp-prompt-server.service
[Unit]
Description=MCP Prompt Server
After=network.target

[Service]
Type=simple
User=your-user
WorkingDirectory=/path/to/project
ExecStart=/usr/bin/node scripts/start.js
Restart=always
Environment=NODE_ENV=production
Environment=PROJECT_ROOT=/path/to/project

[Install]
WantedBy=multi-user.target
```
