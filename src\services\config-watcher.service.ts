/**
 * 配置监听器服务 - 负责监听配置文件变化并触发重新加载
 */

import { EventEmitter } from 'eventemitter3';
import { watch, FSWatcher, existsSync } from 'fs';
import path from 'path';

export interface IConfigWatcher {
  setupWatchers(configPath: string): void;
  watch(configPath: string, callback: (value: unknown) => void): void;
  destroy(): void;
  getActiveWatchersCount(): number;
  on(event: string, listener: (...args: unknown[]) => void): this;
}

export class ConfigWatcher extends EventEmitter implements IConfigWatcher {
  private watchers: FSWatcher[] = [];
  private pathCallbacks = new Map<string, Set<(value: unknown) => void>>();

  /**
   * 设置配置文件监听器
   */
  setupWatchers(configPath: string): void {
    if (!existsSync(configPath)) {
      console.warn(`Configuration file not found for watching: ${configPath}`);
      return;
    }

    try {
      // 监听配置文件变化
      const watcher = watch(configPath, { persistent: false }, eventType => {
        if (eventType === 'change') {
          // Configuration file changed - emit event for listeners
          this.emit('configChanged', configPath);
        }
      });

      this.watchers.push(watcher);

      // 监听配置目录变化（如果配置文件在子目录中）
      const configDir = path.dirname(configPath);
      if (configDir !== '.' && existsSync(configDir)) {
        const dirWatcher = watch(configDir, { persistent: false }, (eventType, filename) => {
          if (eventType === 'change' && filename === path.basename(configPath)) {
            // Configuration file changed in directory - emit event
            this.emit('configChanged', configPath);
          }
        });

        this.watchers.push(dirWatcher);
      }

      // Configuration watcher setup completed
    } catch (error) {
      console.error(`Failed to setup configuration watcher for ${configPath}:`, error);
    }
  }

  /**
   * 监听特定配置路径的变化
   */
  watch(configPath: string, callback: (value: unknown) => void): void {
    if (!this.pathCallbacks.has(configPath)) {
      this.pathCallbacks.set(configPath, new Set());
    }

    this.pathCallbacks.get(configPath)!.add(callback);

    // 监听配置变化事件
    this.on('configChanged', (changedPath: string) => {
      if (changedPath === configPath) {
        const callbacks = this.pathCallbacks.get(configPath);
        if (callbacks) {
          callbacks.forEach(cb => {
            try {
              cb(null); // 传递 null，让调用者重新加载配置
            } catch (error) {
              console.error('Error in config change callback:', error);
            }
          });
        }
      }
    });

    // Watcher added for config path
  }

  /**
   * 移除特定路径的监听器
   */
  unwatch(configPath: string, callback?: (value: unknown) => void): void {
    const callbacks = this.pathCallbacks.get(configPath);
    if (callbacks) {
      if (callback) {
        callbacks.delete(callback);
        if (callbacks.size === 0) {
          this.pathCallbacks.delete(configPath);
        }
      } else {
        this.pathCallbacks.delete(configPath);
      }
    }
  }

  /**
   * 销毁所有监听器
   */
  destroy(): void {
    // 关闭所有文件监听器
    this.watchers.forEach(watcher => {
      try {
        watcher.close();
      } catch (error) {
        console.error('Error closing file watcher:', error);
      }
    });

    this.watchers = [];
    this.pathCallbacks.clear();
    this.removeAllListeners();

    // Configuration watchers destroyed
  }

  /**
   * 获取当前监听的路径数量
   */
  getWatchedPathsCount(): number {
    return this.pathCallbacks.size;
  }

  /**
   * 获取当前活跃的监听器数量
   */
  getActiveWatchersCount(): number {
    return this.watchers.length;
  }

  /**
   * 检查是否正在监听特定路径
   */
  isWatching(configPath: string): boolean {
    return this.pathCallbacks.has(configPath);
  }
}
