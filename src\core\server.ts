/**
 * MCP服务器核心类 - 协调所有组件，提供统一的服务器接口
 * 完整的生产级MCP服务器实现，支持prompts和tools端点
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { EventEmitter } from 'eventemitter3';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import type { ServerConfig } from '../types/config.js';
import type { PromptTemplate } from '../types/prompt.js';
import type { ServerEvents } from '../types/events.js';

import { PromptLoader, type IPromptLoader } from './prompt-loader.js';
import { ToolRegistry, type IToolRegistry } from './tool-registry.js';
import { LoggerService, type ILogger } from '../services/logger.service.js';
import { MetricsService, type IMetricsService } from '../services/metrics.service.js';
import { ResourceLimiterService } from '../services/resource-limiter.service.js';
import { ConfigService, type IConfigService } from '../services/config.service.js';
import {
  MCPClientConfigService,
  type IMCPClientConfigService,
} from '../services/mcp-client-config.service.js';
import {
  MCPClientManager,
  type IMCPClientManager,
} from '../services/mcp-client-manager.service.js';

import { ToolRegistrar, type IToolRegistrar } from './tool-registrar.js';
import { MCPConfigGenerator, type IMCPConfigGenerator } from './mcp-config-generator.js';

export interface IMCPPromptServer {
  start(): Promise<void>;
  stop(): Promise<void>;
  reload(): Promise<void>;
  getStats(): ServerStats;
}

export interface ServerStats {
  uptime: number;
  totalPrompts: number;
  totalTools: number;
  loadedPrompts: string[];
  registeredTools: string[];
}

export class MCPPromptServer extends EventEmitter<ServerEvents> implements IMCPPromptServer {
  private mcpServer: Server;
  private transport: StdioServerTransport;
  private promptLoader: IPromptLoader;
  private toolRegistry: IToolRegistry;
  private logger: ILogger;
  private metrics: IMetricsService;
  private resourceLimiter: ResourceLimiterService;
  private mcpClientConfigService: IMCPClientConfigService;
  private mcpClientManager: IMCPClientManager;
  private toolRegistrar: IToolRegistrar;
  private mcpConfigGenerator: IMCPConfigGenerator;
  private configService: IConfigService;
  private config: ServerConfig;
  private startTime: number = 0;
  private isRunning = false;

  constructor(config: ServerConfig) {
    super();
    this.config = config;

    // 初始化日志服务
    this.logger = new LoggerService(config);

    // 初始化性能监控
    this.metrics = new MetricsService(this.logger);

    // 初始化资源限制器
    this.resourceLimiter = new ResourceLimiterService(config, this.logger);

    // 初始化MCP客户端配置服务
    this.mcpClientConfigService = new MCPClientConfigService(this.logger);

    // 初始化MCP客户端管理器
    this.mcpClientManager = new MCPClientManager(this.logger);

    // 初始化配置服务
    this.configService = new ConfigService();

    // 初始化MCP服务器
    this.mcpServer = new Server(
      {
        name: config.server.name,
        version: config.server.version,
      },
      {
        capabilities: {
          tools: {},
          prompts: {},
        },
      }
    );

    // 初始化传输层
    this.transport = new StdioServerTransport();

    // 初始化组件
    this.promptLoader = new PromptLoader(config);
    this.toolRegistry = new ToolRegistry(this.mcpServer, config);

    // 初始化工具注册器（需要在mcpServer和toolRegistry之后）
    this.toolRegistrar = new ToolRegistrar(
      this.mcpServer,
      this.logger,
      this.toolRegistry,
      this.metrics,
      this.mcpClientConfigService,
      this.mcpClientManager,
      this.promptLoader,
      () => this.startTime,
      this.config,
      () => this.isRunning,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (event: string, ...args: unknown[]) => this.emit(event as any, ...args),
      this.reload.bind(this)
    );

    // 初始化MCP配置生成器
    this.mcpConfigGenerator = new MCPConfigGenerator(
      this.logger,
      this.mcpClientConfigService,
      this.configService
    );

    // 设置MCP处理器
    this.setupMCPHandlers();

    // 设置事件监听
    this.setupEventListeners();
  }

  /**
   * 设置MCP处理器 - 完整的生产级实现
   */
  private setupMCPHandlers(): void {
    // MCP服务器会自动处理以下标准端点：
    // - initialize
    // - tools/list
    // - tools/call

    // 设置服务器能力
    this.setupServerCapabilities();
  }

  /**
   * 设置服务器能力
   */
  private setupServerCapabilities(): void {
    // 工具将在 start() 方法中注册
    // 这里只设置基本的服务器配置
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // Prompt加载器事件
    this.promptLoader.on('prompt:loaded', (prompt: PromptTemplate) => {
      this.emit('prompt:loaded', prompt);
      // 只有在启用 promptsAsTools 时才注册为工具
      if (this.config.server.promptsAsTools) {
        this.toolRegistry.registerTool(prompt);
      }
    });

    this.promptLoader.on('prompts:loaded', (prompts: PromptTemplate[]) => {
      this.emit('prompts:loaded', prompts);
      this.logger.info(`Loaded ${prompts.length} prompts`);
    });

    this.promptLoader.on('error', (error: Error) => {
      this.emit('error', error);
      this.logger.error('Prompt loader error', error);
    });

    // 工具注册器事件
    this.toolRegistry.on('tool:registered', (toolName: string, tool: unknown) => {
      this.emit('tool:registered', toolName, tool);
      this.logger.debug(`Tool registered: ${toolName}`);
    });

    this.toolRegistry.on('error', (error: Error) => {
      this.emit('error', error);
      this.logger.error('Tool registry error', error);
    });

    // 资源限制器事件
    this.resourceLimiter.on('limit:exceeded', (limit: string, current: number, max: number) => {
      this.emit('limit:exceeded', limit, current, max);
      this.logger.warn(`Resource limit exceeded: ${limit} (${current}/${max})`);
    });

    // 性能监控事件（可选）
    // this.metrics.on('metric:recorded', (name: string, value: number) => {
    //   this.logger.debug(`Metric recorded: ${name} = ${value}`);
    // });
  }

  /**
   * 动态解析prompts目录路径
   */
  private resolvePromptDirectories(directories: string[]): string[] {
    // 在ES模块中获取当前文件目录
    const currentFileUrl = import.meta.url;
    const currentFilePath = fileURLToPath(currentFileUrl);
    const scriptDir = path.dirname(currentFilePath);
    // 获取项目根目录（dist的父目录）
    const projectRoot = path.dirname(scriptDir);

    this.logger.debug(`Script directory: ${scriptDir}`);
    this.logger.debug(`Project root: ${projectRoot}`);

    const resolvedDirs: string[] = [];

    for (const dir of directories) {
      let resolvedPath: string;

      if (path.isAbsolute(dir)) {
        // 绝对路径直接使用
        resolvedPath = dir;
      } else {
        // 相对路径：先尝试相对于项目根目录，再尝试相对于当前工作目录
        const rootRelativePath = path.resolve(projectRoot, dir);
        const cwdRelativePath = path.resolve(process.cwd(), dir);

        // 检查哪个路径存在
        if (fs.existsSync(rootRelativePath)) {
          resolvedPath = rootRelativePath;
          this.logger.debug(`Found directory relative to project root: ${resolvedPath}`);
        } else if (fs.existsSync(cwdRelativePath)) {
          resolvedPath = cwdRelativePath;
          this.logger.debug(`Found directory relative to cwd: ${resolvedPath}`);
        } else {
          // 都不存在，使用项目根目录相对路径作为默认
          resolvedPath = rootRelativePath;
          this.logger.warn(`Directory not found, using default: ${resolvedPath}`);
        }
      }

      resolvedDirs.push(resolvedPath);
    }

    return resolvedDirs;
  }

  /**
   * 启动服务器
   */
  async start(): Promise<void> {
    try {
      this.startTime = Date.now();
      this.isRunning = true;

      this.logger.info('Starting MCP Prompt Server...');
      this.logger.info(`Current working directory: ${process.cwd()}`);

      // 动态解析prompts目录路径
      const resolvedDirectories = this.resolvePromptDirectories(this.config.prompts.directories);
      this.logger.info(`Resolved prompt directories: ${JSON.stringify(resolvedDirectories)}`);
      this.emit('server:starting');

      // 加载prompts
      const loadResult = await this.promptLoader.loadPrompts(resolvedDirectories);

      // 记录加载统计
      this.metrics.recordMetric('prompts_loaded', loadResult.prompts.length);
      this.metrics.incrementCounter('server_starts');

      // 注册所有工具
      this.registerAllTools();

      // 启动文件监听（如果启用）
      if (this.config.prompts.watchForChanges) {
        this.promptLoader.startWatching();
      }

      // 连接传输层
      await this.mcpServer.connect(this.transport);

      this.logger.info(
        `MCP Prompt Server started successfully with ${loadResult.prompts.length} prompts`
      );
      this.emit('server:started');
    } catch (error) {
      this.isRunning = false;
      this.logger.error('Failed to start server', error as Error);
      this.emit('server:error', error as Error);
      throw error;
    }
  }

  /**
   * 停止服务器
   */
  async stop(): Promise<void> {
    try {
      this.logger.info('Stopping MCP Prompt Server...');
      this.emit('server:stopping');

      this.isRunning = false;

      // 停止文件监听
      this.promptLoader.stopWatching();

      // 断开传输层
      await this.transport.close();

      // 清理资源
      this.resourceLimiter.destroy();
      this.metrics.destroy();

      this.logger.info('MCP Prompt Server stopped successfully');
      this.emit('server:stopped');
    } catch (error) {
      this.logger.error('Error stopping server', error as Error);
      this.emit('server:error', error as Error);
      throw error;
    }
  }

  /**
   * 重新加载prompts
   */
  async reload(): Promise<void> {
    try {
      this.logger.info('Reloading prompts...');
      this.emit('prompts:reloading');

      // 重新加载prompts
      const loadResult = await this.promptLoader.loadPrompts(this.config.prompts.directories);

      // 如果启用了 promptsAsTools，重新注册工具
      if (this.config.server.promptsAsTools) {
        this.hotReloadPrompts(loadResult.prompts);
      }

      // 更新指标
      this.metrics.recordMetric('prompts_loaded', loadResult.prompts.length);
      this.metrics.incrementCounter('prompts_reloaded');

      this.logger.info(`Reloaded ${loadResult.prompts.length} prompts`);
      this.emit('prompts:reloaded', loadResult.prompts);
    } catch (error) {
      this.logger.error('Failed to reload prompts', error as Error);
      this.emit('error', error as Error);
      throw error;
    }
  }

  /**
   * 热重载prompts
   */
  private hotReloadPrompts(prompts: PromptTemplate[]): void {
    // 清除现有工具
    this.toolRegistry.clearAllTools();

    // 重新注册工具
    for (const prompt of prompts) {
      this.toolRegistry.registerTool(prompt);
    }

    this.logger.info(`Hot reloaded ${prompts.length} prompts`);
  }

  /**
   * 注册所有工具
   */
  private registerAllTools(): void {
    // 使用工具注册器注册所有工具
    this.toolRegistrar.registerAllTools();

    // 注册MCP配置生成器工具到统一注册表
    const unifiedRegistry = this.toolRegistrar.getUnifiedRegistry();
    this.mcpConfigGenerator.registerTool(unifiedRegistry);
  }

  /**
   * 获取服务器统计信息
   */
  getStats(): ServerStats {
    const loadedPrompts = this.promptLoader.getLoadedPrompts();
    const registeredTools = this.toolRegistry.getRegisteredTools();

    return {
      uptime: this.startTime > 0 ? Date.now() - this.startTime : 0,
      totalPrompts: loadedPrompts.length,
      totalTools: registeredTools.length,
      loadedPrompts: loadedPrompts.map(p => p.name),
      registeredTools,
    };
  }

  /**
   * 获取配置信息
   */
  getConfig(): ServerConfig {
    return { ...this.config };
  }

  /**
   * 清理资源
   */
  destroy(): void {
    if (this.isRunning) {
      this.stop().catch(error => {
        this.logger.error('Error during shutdown', error);
      });
    }

    // 清理所有服务
    this.promptLoader.destroy();
    this.toolRegistry.destroy();
    this.metrics.destroy();
    this.resourceLimiter.destroy();
    this.logger.destroy();
    this.removeAllListeners();
  }
}
