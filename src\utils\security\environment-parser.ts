/**
 * 企业级环境变量解析器
 * 提供安全的环境变量解析和验证功能
 */

/**
 * 生产级日志记录器
 * 在生产环境中提供安全的日志记录
 */
export class ProductionLogger {
  /**
   * 记录信息日志 - 生产环境友好
   */
  static log(message: string, ...args: unknown[]): void {
    // 在生产环境和MCP环境中都保持静默，避免干扰stdio通信
    if (process.env['NODE_ENV'] !== 'production' && process.env['MCP_DEBUG'] === 'true') {
      // eslint-disable-next-line no-console
      console.log(`[INFO] ${message}`, ...args);
    }
  }

  /**
   * 记录错误日志
   */
  static error(message: string, error?: unknown): void {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`[ERROR] ${message}`, errorMessage);
  }

  /**
   * 记录警告日志
   */
  static warn(message: string, ...args: unknown[]): void {
    console.warn(`[WARN] ${message}`, ...args);
  }

  /**
   * 记录调试日志（仅在开发环境）
   */
  static debug(message: string, ...args: unknown[]): void {
    if (process.env['NODE_ENV'] === 'development') {
      // eslint-disable-next-line no-console
      console.debug(`[DEBUG] ${message}`, ...args);
    }
  }
}

/**
 * 企业级环境变量解析器
 * 提供安全的类型转换和验证功能
 */
export class EnvironmentParser {
  /**
   * 安全解析整数环境变量
   */
  static parseInt(
    value: string | undefined,
    defaultValue: number,
    min?: number,
    max?: number
  ): number {
    if (!value) {
      return defaultValue;
    }

    // 验证输入格式
    if (!/^-?\d+$/.test(value.trim())) {
      ProductionLogger.warn(`Invalid integer format for environment variable: ${value}`);
      return defaultValue;
    }

    const parsed = parseInt(value.trim(), 10);

    if (isNaN(parsed)) {
      ProductionLogger.warn(`Failed to parse integer from environment variable: ${value}`);
      return defaultValue;
    }

    // 范围验证
    if (min !== undefined && parsed < min) {
      ProductionLogger.warn(`Environment variable value ${parsed} is below minimum ${min}`);
      return defaultValue;
    }

    if (max !== undefined && parsed > max) {
      ProductionLogger.warn(`Environment variable value ${parsed} is above maximum ${max}`);
      return defaultValue;
    }

    return parsed;
  }

  /**
   * 安全解析浮点数环境变量
   */
  static parseFloat(
    value: string | undefined,
    defaultValue: number,
    min?: number,
    max?: number
  ): number {
    if (!value) {
      return defaultValue;
    }

    // 验证输入格式
    if (!/^-?\d+(\.\d+)?$/.test(value.trim())) {
      ProductionLogger.warn(`Invalid float format for environment variable: ${value}`);
      return defaultValue;
    }

    const parsed = parseFloat(value.trim());

    if (isNaN(parsed) || !isFinite(parsed)) {
      ProductionLogger.warn(`Failed to parse float from environment variable: ${value}`);
      return defaultValue;
    }

    // 范围验证
    if (min !== undefined && parsed < min) {
      ProductionLogger.warn(`Environment variable value ${parsed} is below minimum ${min}`);
      return defaultValue;
    }

    if (max !== undefined && parsed > max) {
      ProductionLogger.warn(`Environment variable value ${parsed} is above maximum ${max}`);
      return defaultValue;
    }

    return parsed;
  }

  /**
   * 安全解析布尔值环境变量
   */
  static parseBoolean(value: string | undefined, defaultValue: boolean): boolean {
    if (!value) {
      return defaultValue;
    }

    const normalized = value.trim().toLowerCase();

    // 支持的真值
    if (['true', '1', 'yes', 'on', 'enabled'].includes(normalized)) {
      return true;
    }

    // 支持的假值
    if (['false', '0', 'no', 'off', 'disabled'].includes(normalized)) {
      return false;
    }

    ProductionLogger.warn(`Invalid boolean format for environment variable: ${value}`);
    return defaultValue;
  }

  /**
   * 安全解析字符串环境变量
   */
  static parseString(value: string | undefined, defaultValue: string, maxLength?: number): string {
    if (!value) {
      return defaultValue;
    }

    const trimmed = value.trim();

    // 长度验证
    if (maxLength !== undefined && trimmed.length > maxLength) {
      ProductionLogger.warn(
        `Environment variable string too long: ${trimmed.length} > ${maxLength}`
      );
      return defaultValue;
    }

    // 基本安全检查
    if (trimmed.includes('\0')) {
      ProductionLogger.warn('Environment variable contains null bytes');
      return defaultValue;
    }

    return trimmed;
  }

  /**
   * 安全解析数组环境变量（逗号分隔）
   */
  static parseArray(
    value: string | undefined,
    defaultValue: string[],
    maxItems?: number
  ): string[] {
    if (!value) {
      return defaultValue;
    }

    const items = value
      .split(',')
      .map(item => item.trim())
      .filter(item => item.length > 0);

    // 数量验证
    if (maxItems !== undefined && items.length > maxItems) {
      ProductionLogger.warn(`Environment variable array too long: ${items.length} > ${maxItems}`);
      return defaultValue;
    }

    // 安全检查每个项目
    for (const item of items) {
      if (item.includes('\0')) {
        ProductionLogger.warn('Environment variable array item contains null bytes');
        return defaultValue;
      }
    }

    return items;
  }

  /**
   * 验证环境变量是否存在
   */
  static validateRequired(name: string): string {
    const value = process.env[name];
    if (!value) {
      throw new Error(`Required environment variable ${name} is not set`);
    }
    return value;
  }

  /**
   * 获取环境变量摘要（用于日志记录，过滤敏感信息）
   */
  static getEnvironmentSummary(): Record<string, string> {
    const summary: Record<string, string> = {};
    const sensitiveKeys = ['password', 'secret', 'key', 'token', 'auth'];

    for (const [key, value] of Object.entries(process.env)) {
      if (key.startsWith('SECURITY_') || key.startsWith('MCP_')) {
        const isSensitive = sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive));

        if (isSensitive) {
          summary[key] = '[REDACTED]';
        } else {
          summary[key] = value || '[EMPTY]';
        }
      }
    }

    return summary;
  }

  /**
   * 验证环境变量格式
   */
  static validateEnvironmentFormat(): void {
    const requiredVars = ['NODE_ENV'];

    const warnings: string[] = [];

    for (const varName of requiredVars) {
      if (!process.env[varName]) {
        warnings.push(`Missing environment variable: ${varName}`);
      }
    }

    if (warnings.length > 0) {
      ProductionLogger.warn('Environment validation warnings:', warnings);
    }
  }
}
