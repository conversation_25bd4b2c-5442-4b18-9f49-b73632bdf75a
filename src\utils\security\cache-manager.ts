/**
 * 企业级缓存管理器
 * 提供高性能的LRU缓存机制，支持TTL和自动清理
 */

import type { CacheConfig } from './types.js';
import { getCacheConfig } from './config.js';
import { ProductionLogger } from './environment-parser.js';

/**
 * 缓存项接口
 */
interface CacheItem<T> {
  value: T;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
}

/**
 * 缓存统计信息
 */
interface CacheStats {
  hits: number;
  misses: number;
  evictions: number;
  size: number;
  maxSize: number;
  hitRate: number;
}

/**
 * 企业级LRU缓存管理器
 * 支持TTL、自动清理、性能监控等功能
 */
export class CacheManager<T> {
  private cache = new Map<string, CacheItem<T>>();
  private config: CacheConfig;
  private stats = {
    hits: 0,
    misses: 0,
    evictions: 0,
  };
  private cleanupTimer?: NodeJS.Timeout | undefined;

  constructor(customConfig?: Partial<CacheConfig>) {
    this.config = { ...getCacheConfig(), ...customConfig };

    if (this.config.enabled) {
      this.startCleanupTimer();
    }
  }

  /**
   * 获取缓存项
   */
  get(key: string): T | undefined {
    if (!this.config.enabled) {
      return undefined;
    }

    const item = this.cache.get(key);

    if (!item) {
      this.stats.misses++;
      return undefined;
    }

    // 检查TTL
    if (this.isExpired(item)) {
      this.cache.delete(key);
      this.stats.misses++;
      return undefined;
    }

    // 更新访问信息
    item.accessCount++;
    item.lastAccessed = Date.now();
    this.stats.hits++;

    return item.value;
  }

  /**
   * 设置缓存项
   */
  set(key: string, value: T): void {
    if (!this.config.enabled) {
      return;
    }

    // 检查缓存大小限制
    if (this.cache.size >= this.config.maxSize && !this.cache.has(key)) {
      this.evictLRU();
    }

    const now = Date.now();
    const item: CacheItem<T> = {
      value,
      timestamp: now,
      accessCount: 1,
      lastAccessed: now,
    };

    this.cache.set(key, item);
  }

  /**
   * 删除缓存项
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
    this.resetStats();
  }

  /**
   * 检查缓存项是否存在
   */
  has(key: string): boolean {
    if (!this.config.enabled) {
      return false;
    }

    const item = this.cache.get(key);
    if (!item) {
      return false;
    }

    if (this.isExpired(item)) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * 获取缓存大小
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    const total = this.stats.hits + this.stats.misses;
    return {
      ...this.stats,
      size: this.cache.size,
      maxSize: this.config.maxSize,
      hitRate: total > 0 ? this.stats.hits / total : 0,
    };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats.hits = 0;
    this.stats.misses = 0;
    this.stats.evictions = 0;
  }

  /**
   * 检查缓存项是否过期
   */
  private isExpired(item: CacheItem<T>): boolean {
    return Date.now() - item.timestamp > this.config.ttl;
  }

  /**
   * 驱逐最少使用的缓存项
   */
  private evictLRU(): void {
    let lruKey: string | undefined;
    let lruTime = Date.now();

    for (const [key, item] of this.cache.entries()) {
      if (item.lastAccessed < lruTime) {
        lruTime = item.lastAccessed;
        lruKey = key;
      }
    }

    if (lruKey) {
      this.cache.delete(lruKey);
      this.stats.evictions++;
    }
  }

  /**
   * 清理过期的缓存项
   */
  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > this.config.ttl) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));

    if (keysToDelete.length > 0) {
      ProductionLogger.debug(`Cleaned up ${keysToDelete.length} expired cache items`);
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * 停止清理定时器
   */
  stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined as undefined;
    }
  }

  /**
   * 销毁缓存管理器
   */
  destroy(): void {
    this.stopCleanupTimer();
    this.clear();
  }
}

/**
 * 全局缓存实例管理器
 */
class GlobalCacheManager {
  private static caches = new Map<string, CacheManager<unknown>>();

  /**
   * 获取或创建缓存实例
   */
  static getCache<T>(name: string, config?: Partial<CacheConfig>): CacheManager<T> {
    if (!this.caches.has(name)) {
      this.caches.set(name, new CacheManager<T>(config));
    }
    return this.caches.get(name) as CacheManager<T>;
  }

  /**
   * 销毁缓存实例
   */
  static destroyCache(name: string): void {
    const cache = this.caches.get(name);
    if (cache) {
      cache.destroy();
      this.caches.delete(name);
    }
  }

  /**
   * 获取所有缓存的统计信息
   */
  static getAllStats(): Record<string, CacheStats> {
    const stats: Record<string, CacheStats> = {};
    for (const [name, cache] of this.caches.entries()) {
      stats[name] = cache.getStats();
    }
    return stats;
  }

  /**
   * 清空所有缓存
   */
  static clearAll(): void {
    for (const cache of this.caches.values()) {
      cache.clear();
    }
  }

  /**
   * 销毁所有缓存
   */
  static destroyAll(): void {
    for (const [name] of this.caches.entries()) {
      this.destroyCache(name);
    }
  }
}

// 预定义的缓存实例
export const patternCache = GlobalCacheManager.getCache<RegExp>('patterns');
export const validationCache = GlobalCacheManager.getCache<boolean>('validation');
export const expressionCache = GlobalCacheManager.getCache<unknown>('expressions');

// 导出全局缓存管理器
export { GlobalCacheManager };
