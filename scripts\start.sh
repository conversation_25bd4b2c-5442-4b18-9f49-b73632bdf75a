#!/bin/bash

# MCP Prompt Server - Unix/Linux 启动脚本
# 自动检测环境并设置正确的路径配置

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 获取脚本目录和项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo -e "${BLUE}🚀 Starting MCP Prompt Server (Unix/Linux)${NC}"
echo -e "${CYAN}📁 Project Root: ${PROJECT_ROOT}${NC}"
echo -e "${CYAN}🖥️  Platform: $(uname -s)${NC}"
echo ""

# 设置环境变量（如果未设置）
export PROJECT_ROOT="${PROJECT_ROOT}"
export NODE_ENV="${NODE_ENV:-development}"

# 设置 Prompt 目录
if [ -z "$MCP_PROMPTS_DIRECTORIES" ]; then
    export MCP_PROMPTS_DIRECTORIES="./dist/prompts,./dist/custom-prompts,./src/prompts,./custom-prompts"
fi

# 设置日志配置
if [ -z "$MCP_LOG_LEVEL" ]; then
    if [ "$NODE_ENV" = "production" ]; then
        export MCP_LOG_LEVEL="info"
    else
        export MCP_LOG_LEVEL="debug"
    fi
fi

if [ -z "$MCP_LOG_FORMAT" ]; then
    if [ "$NODE_ENV" = "production" ]; then
        export MCP_LOG_FORMAT="json"
    else
        export MCP_LOG_FORMAT="text"
    fi
fi

# 设置安全配置
if [ -z "$SECURITY_STRICT_MODE" ]; then
    if [ "$NODE_ENV" = "production" ]; then
        export SECURITY_STRICT_MODE="true"
    else
        export SECURITY_STRICT_MODE="false"
    fi
fi

echo -e "${YELLOW}🔧 Environment: ${NODE_ENV}${NC}"
echo -e "${YELLOW}📂 Prompt Directories: ${MCP_PROMPTS_DIRECTORIES}${NC}"
echo -e "${YELLOW}📊 Log Level: ${MCP_LOG_LEVEL}${NC}"
echo -e "${YELLOW}🔒 Security Mode: ${SECURITY_STRICT_MODE}${NC}"
echo ""

# 检查主程序文件
DIST_SCRIPT="${PROJECT_ROOT}/dist/index.js"
SRC_SCRIPT="${PROJECT_ROOT}/src/index.ts"
MAIN_SCRIPT=""
USE_NODE=0
USE_TSX=0

if [ -f "$DIST_SCRIPT" ]; then
    MAIN_SCRIPT="$DIST_SCRIPT"
    USE_NODE=1
    echo -e "${GREEN}🎯 Using compiled script: ${DIST_SCRIPT}${NC}"
elif [ -f "$SRC_SCRIPT" ]; then
    MAIN_SCRIPT="$SRC_SCRIPT"
    USE_TSX=1
    echo -e "${GREEN}🎯 Using TypeScript script: ${SRC_SCRIPT}${NC}"
else
    echo -e "${RED}❌ Error: Cannot find main script${NC}"
    echo -e "${RED}   Looked for: ${DIST_SCRIPT}${NC}"
    echo -e "${RED}   Looked for: ${SRC_SCRIPT}${NC}"
    echo -e "${RED}   Please build the project first with: npm run build${NC}"
    exit 1
fi

echo "─────────────────────────────────────────────────────"
echo ""

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 信号处理函数
cleanup() {
    echo ""
    echo -e "${YELLOW}🛑 Received interrupt signal, stopping server...${NC}"
    if [ ! -z "$SERVER_PID" ]; then
        kill -TERM "$SERVER_PID" 2>/dev/null || true
        wait "$SERVER_PID" 2>/dev/null || true
    fi
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 启动服务器
if [ $USE_NODE -eq 1 ]; then
    echo -e "${GREEN}🚀 Starting with Node.js...${NC}"
    node "$MAIN_SCRIPT" &
    SERVER_PID=$!
elif [ $USE_TSX -eq 1 ]; then
    echo -e "${GREEN}🚀 Starting with tsx...${NC}"
    npx tsx "$MAIN_SCRIPT" &
    SERVER_PID=$!
fi

# 等待服务器进程
wait "$SERVER_PID"
EXIT_CODE=$?

# 检查退出代码
if [ $EXIT_CODE -ne 0 ]; then
    echo ""
    echo -e "${RED}❌ Server exited with error code: ${EXIT_CODE}${NC}"
    exit $EXIT_CODE
else
    echo ""
    echo -e "${GREEN}✅ Server stopped gracefully${NC}"
fi
