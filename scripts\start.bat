@echo off
REM MCP Prompt Server - Windows 启动脚本
REM 自动检测环境并设置正确的路径配置

setlocal enabledelayedexpansion

REM 获取脚本目录和项目根目录
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%.."

REM 标准化路径（移除末尾的反斜杠）
if "%PROJECT_ROOT:~-1%"=="\" set "PROJECT_ROOT=%PROJECT_ROOT:~0,-1%"

echo.
echo 🚀 Starting MCP Prompt Server (Windows)
echo 📁 Project Root: %PROJECT_ROOT%
echo 🖥️  Platform: Windows
echo.

REM 设置环境变量（如果未设置）
if not defined PROJECT_ROOT set "PROJECT_ROOT=%PROJECT_ROOT%"
if not defined NODE_ENV set "NODE_ENV=development"

REM 设置 Prompt 目录
if not defined MCP_PROMPTS_DIRECTORIES (
    set "MCP_PROMPTS_DIRECTORIES=./dist/prompts,./dist/custom-prompts,./src/prompts,./custom-prompts"
)

REM 设置日志配置
if not defined MCP_LOG_LEVEL (
    if "%NODE_ENV%"=="production" (
        set "MCP_LOG_LEVEL=info"
    ) else (
        set "MCP_LOG_LEVEL=debug"
    )
)

if not defined MCP_LOG_FORMAT (
    if "%NODE_ENV%"=="production" (
        set "MCP_LOG_FORMAT=json"
    ) else (
        set "MCP_LOG_FORMAT=text"
    )
)

REM 设置安全配置
if not defined SECURITY_STRICT_MODE (
    if "%NODE_ENV%"=="production" (
        set "SECURITY_STRICT_MODE=true"
    ) else (
        set "SECURITY_STRICT_MODE=false"
    )
)

echo 🔧 Environment: %NODE_ENV%
echo 📂 Prompt Directories: %MCP_PROMPTS_DIRECTORIES%
echo 📊 Log Level: %MCP_LOG_LEVEL%
echo 🔒 Security Mode: %SECURITY_STRICT_MODE%
echo.

REM 检查主程序文件
set "DIST_SCRIPT=%PROJECT_ROOT%\dist\index.js"
set "SRC_SCRIPT=%PROJECT_ROOT%\src\index.ts"
set "MAIN_SCRIPT="

if exist "%DIST_SCRIPT%" (
    set "MAIN_SCRIPT=%DIST_SCRIPT%"
    set "USE_NODE=1"
    echo 🎯 Using compiled script: %DIST_SCRIPT%
) else if exist "%SRC_SCRIPT%" (
    set "MAIN_SCRIPT=%SRC_SCRIPT%"
    set "USE_TSX=1"
    echo 🎯 Using TypeScript script: %SRC_SCRIPT%
) else (
    echo ❌ Error: Cannot find main script
    echo    Looked for: %DIST_SCRIPT%
    echo    Looked for: %SRC_SCRIPT%
    echo    Please build the project first with: npm run build
    pause
    exit /b 1
)

echo ─────────────────────────────────────────────────────
echo.

REM 切换到项目根目录
cd /d "%PROJECT_ROOT%"

REM 启动服务器
if defined USE_NODE (
    echo 🚀 Starting with Node.js...
    node "%MAIN_SCRIPT%"
) else if defined USE_TSX (
    echo 🚀 Starting with tsx...
    npx tsx "%MAIN_SCRIPT%"
)

REM 检查退出代码
if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ Server exited with error code: %ERRORLEVEL%
    pause
    exit /b %ERRORLEVEL%
) else (
    echo.
    echo ✅ Server stopped gracefully
)

pause
