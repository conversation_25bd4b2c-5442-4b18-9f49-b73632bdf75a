/**
 * 变量替换器模块
 * 专门处理模板中的变量替换逻辑
 */

import type { ExpressionContext, SecurityEvent } from '../security/types.js';
import { getSecurityConfig } from '../security/config.js';

/**
 * 变量替换器类
 * 提供安全的变量替换功能
 */
export class VariableReplacer {
  private static securityEventHandlers: Array<(event: SecurityEvent) => void> = [];

  /**
   * 添加安全事件处理器
   */
  static addSecurityEventHandler(handler: (event: SecurityEvent) => void): void {
    this.securityEventHandlers.push(handler);
  }

  /**
   * 触发安全事件
   */
  private static emitSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): void {
    const fullEvent: SecurityEvent = {
      ...event,
      timestamp: new Date(),
    };

    this.securityEventHandlers.forEach(handler => {
      try {
        handler(fullEvent);
      } catch (error) {
        console.error('Security event handler failed:', error);
      }
    });
  }

  /**
   * 安全地替换字符串中的变量（增强版本）
   * 提供更严格的安全控制和错误处理
   */
  static replaceVariables(template: string, context: ExpressionContext): string {
    try {
      const config = getSecurityConfig();

      // 输入验证
      if (typeof template !== 'string') {
        throw new Error('Template must be a string');
      }

      if (template.length > config.maxTemplateLength) {
        throw new Error('Template too long');
      }

      // 检查模板中的变量数量（防止DoS攻击）- 支持 {variable} 格式
      const variableMatches = template.match(/\{[^}]+\}/g) || [];
      if (variableMatches.length > 50) {
        // 最多50个变量
        throw new Error('Too many variables in template');
      }

      // 安全的变量替换 - 支持 {variable} 格式
      return template.replace(
        /\{([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\}/g,
        (match, varPath) => {
          try {
            // 验证变量路径深度（防止深度遍历攻击）
            const pathParts = varPath.split('.');
            if (pathParts.length > 10) {
              // 最大10层深度
              this.emitSecurityEvent({
                type: 'security_violation',
                message: `Variable path too deep: ${varPath}`,
                input: template,
                severity: 'medium',
              });
              return match; // 保留原始占位符
            }

            const value = this.getNestedValue(context, varPath);
            // 如果变量不存在，保留原始占位符
            if (value === undefined) {
              return match;
            }
            return this.sanitizeValue(value);
          } catch (error) {
            // 记录变量访问失败
            this.emitSecurityEvent({
              type: 'validation_failed',
              message: `Variable access failed: ${varPath}`,
              input: template,
              severity: 'low',
            });
            return match; // 保留原始占位符
          }
        }
      );
    } catch (error) {
      this.emitSecurityEvent({
        type: 'validation_failed',
        message: `Variable replacement failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        input: template,
        severity: 'low',
      });
      return template; // 返回原始模板
    }
  }

  /**
   * 安全地获取嵌套对象值（增强循环引用检测）
   */
  private static getNestedValue(obj: ExpressionContext, path: string): unknown {
    const keys = path.split('.');
    let current: unknown = obj;
    const visited = new Set<unknown>();

    for (const key of keys) {
      // 验证属性名的安全性
      if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(key)) {
        throw new Error('Invalid property name');
      }

      if (current === null || current === undefined) {
        return undefined;
      }

      // 检测循环引用
      if (typeof current === 'object' && current !== null) {
        if (visited.has(current)) {
          throw new Error('Circular reference detected');
        }
        visited.add(current);
        current = (current as Record<string, unknown>)[key];
      } else {
        return undefined;
      }
    }

    return current;
  }

  /**
   * 安全地清理值
   */
  private static sanitizeValue(value: unknown): string {
    if (value === null || value === undefined) {
      return '';
    }

    const str = String(value);
    const config = getSecurityConfig();

    // 限制长度防止DoS
    if (str.length > config.maxStringLength) {
      return `${str.substring(0, config.maxStringLength)}...`;
    }

    // 移除控制字符和潜在的脚本内容
    return (
      str
        // eslint-disable-next-line no-control-regex
        .replace(/[\u0000-\u001F\u007F-\u009F]/g, '')
        .replace(/<script[^>]*>.*?<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
    );
  }
}
