/**
 * Prompt加载器 - 负责从文件系统加载和管理Prompt模板
 */

import { promises as fs } from 'fs';
import path from 'path';
import YAML from 'yaml';
import chokidar from 'chokidar';
import { EventEmitter } from 'eventemitter3';
import type { PromptTemplate } from '../types/prompt.js';
import type { ServerConfig } from '../types/config.js';
import { MarkdownPromptParser } from '../utils/markdown-parser.js';

export interface PromptLoadResult {
  success: boolean;
  prompts: PromptTemplate[];
  errors: PromptLoadError[];
}

export interface PromptLoadError {
  file: string;
  error: string;
  line?: number;
  column?: number;
}

export interface IPromptLoader extends EventEmitter {
  loadPrompts(directories: string[]): Promise<PromptLoadResult>;
  watchForChanges(callback: (prompts: PromptTemplate[]) => void): void;
  startWatching(): void;
  getLoadedPrompts(): PromptTemplate[];
  getPromptByName(name: string): PromptTemplate | undefined;
  reloadPrompts(): Promise<PromptLoadResult>;
  stopWatching(): void;
  destroy(): void;
}

export class PromptLoader extends EventEmitter implements IPromptLoader {
  private loadedPrompts: PromptTemplate[] = [];
  private watchers: chokidar.FSWatcher[] = [];
  private config: ServerConfig;
  private isWatching = false;

  constructor(config: ServerConfig) {
    super();
    this.config = config;
  }

  /**
   * 从指定目录加载所有Prompt模板
   */
  async loadPrompts(directories: string[]): Promise<PromptLoadResult> {
    const prompts: PromptTemplate[] = [];
    const errors: PromptLoadError[] = [];

    for (const directory of directories) {
      try {
        // 检查目录是否存在
        try {
          await fs.access(directory);
        } catch {
          // 目录不存在，记录错误
          errors.push({
            file: directory,
            error: `Directory does not exist: ${directory}`,
          });
          continue;
        }
        const result = await this.loadPromptsFromDirectory(directory);
        prompts.push(...result.prompts);
        errors.push(...result.errors);
      } catch (error) {
        errors.push({
          file: directory,
          error: `Failed to access directory: ${error instanceof Error ? error.message : String(error)}`,
        });
      }
    }

    this.loadedPrompts = prompts;
    this.emit('prompts:loaded', prompts);

    return {
      success: errors.length === 0,
      prompts,
      errors,
    };
  }

  /**
   * 从单个目录加载Prompt模板
   */
  private async loadPromptsFromDirectory(directory: string): Promise<PromptLoadResult> {
    const prompts: PromptTemplate[] = [];
    const errors: PromptLoadError[] = [];

    try {
      const files = await fs.readdir(directory, { withFileTypes: true });

      for (const file of files) {
        if (file.isFile() && this.isSupportedFile(file.name)) {
          const filePath = path.join(directory, file.name);

          try {
            const prompt = await this.loadPromptFromFile(filePath);
            if (prompt) {
              prompts.push(prompt);
              this.emit('prompt:loaded', prompt);
            }
          } catch (error) {
            errors.push({
              file: filePath,
              error: error instanceof Error ? error.message : String(error),
            });
          }
        }
      }
    } catch (error) {
      errors.push({
        file: directory,
        error: `Failed to read directory: ${error instanceof Error ? error.message : String(error)}`,
      });
    }

    return { success: errors.length === 0, prompts, errors };
  }

  /**
   * 从单个文件加载Prompt模板
   */
  private async loadPromptFromFile(filePath: string): Promise<PromptTemplate | null> {
    const stats = await fs.stat(filePath);

    // 检查文件大小限制
    if (stats.size > this.config.prompts.maxFileSize) {
      throw new Error(
        `File size exceeds limit: ${stats.size} > ${this.config.prompts.maxFileSize}`
      );
    }

    const content = await fs.readFile(filePath, 'utf8');
    const ext = path.extname(filePath).toLowerCase();

    let prompt: unknown;

    try {
      switch (ext) {
        case '.json':
          prompt = JSON.parse(content);
          break;
        case '.yaml':
        case '.yml':
          prompt = YAML.parse(content);
          break;
        case '.js':
        case '.ts': {
          // 动态导入JS/TS文件
          const module = await import(filePath);
          prompt = module.default || module;
          break;
        }
        case '.md': {
          // 解析 Markdown 提示文件
          if (!MarkdownPromptParser.validateMarkdownPrompt(content)) {
            throw new Error('Invalid Markdown prompt format');
          }
          prompt = MarkdownPromptParser.parseMarkdownPrompt(content, path.basename(filePath));
          break;
        }
        default:
          throw new Error(`Unsupported file format: ${ext}`);
      }
    } catch (error) {
      throw new Error(
        `Failed to parse file: ${error instanceof Error ? error.message : String(error)}`
      );
    }

    // 验证Prompt格式
    const validatedPrompt = this.validatePrompt(prompt, filePath);
    return validatedPrompt;
  }

  /**
   * 验证Prompt模板格式
   */
  private validatePrompt(prompt: unknown, _filePath: string): PromptTemplate {
    if (!prompt || typeof prompt !== 'object') {
      throw new Error('Prompt must be an object');
    }

    const p = prompt as Record<string, unknown>;

    if (!p['name'] || typeof p['name'] !== 'string') {
      throw new Error('Prompt must have a valid name field');
    }

    if (!p['description'] || typeof p['description'] !== 'string') {
      throw new Error('Prompt must have a valid description field');
    }

    if (!p['messages'] || !Array.isArray(p['messages']) || p['messages'].length === 0) {
      throw new Error('Prompt must have at least one message');
    }

    // 验证消息格式
    for (const [index, message] of p['messages'].entries()) {
      if (!message || typeof message !== 'object') {
        throw new Error(`Message at index ${index} must be an object`);
      }

      const msg = message as Record<string, unknown>;
      if (!msg['role'] || !['user', 'assistant', 'system'].includes(msg['role'] as string)) {
        throw new Error(`Message at index ${index} must have a valid role`);
      }

      if (!msg['content'] || typeof msg['content'] !== 'object') {
        throw new Error(`Message at index ${index} must have valid content`);
      }
    }

    return prompt as unknown as PromptTemplate;
  }

  /**
   * 检查文件是否为支持的格式
   */
  private isSupportedFile(filename: string): boolean {
    const ext = path.extname(filename).toLowerCase();
    return this.config.prompts.supportedFormats.includes(ext.slice(1));
  }

  /**
   * 监听文件变化
   */
  watchForChanges(callback: (prompts: PromptTemplate[]) => void): void {
    if (!this.config.prompts.watchForChanges || this.isWatching) {
      return;
    }

    this.isWatching = true;

    for (const directory of this.config.prompts.directories) {
      const watcher = chokidar.watch(directory, {
        ignored: /node_modules/,
        persistent: true,
        ignoreInitial: true,
      });

      watcher.on('add', () => this.handleFileChange(callback));
      watcher.on('change', () => this.handleFileChange(callback));
      watcher.on('unlink', () => this.handleFileChange(callback));

      this.watchers.push(watcher);
    }

    this.emit('watching:started');
  }

  /**
   * 开始监听文件变化
   */
  startWatching(): void {
    if (this.config.prompts.watchForChanges) {
      this.watchForChanges((prompts: PromptTemplate[]) => {
        this.emit('prompts:reloaded', prompts);
      });
    }
  }

  /**
   * 处理文件变化事件
   */
  private async handleFileChange(callback: (prompts: PromptTemplate[]) => void): Promise<void> {
    try {
      const result = await this.reloadPrompts();
      if (result.success) {
        callback(result.prompts);
        this.emit('prompts:reloaded', result.prompts);
      } else {
        this.emit('prompts:error', result.errors);
      }
    } catch (error) {
      this.emit('error', error);
    }
  }

  /**
   * 重新加载所有Prompt模板
   */
  async reloadPrompts(): Promise<PromptLoadResult> {
    return this.loadPrompts(this.config.prompts.directories);
  }

  /**
   * 获取已加载的Prompt模板
   */
  getLoadedPrompts(): PromptTemplate[] {
    return [...this.loadedPrompts];
  }

  /**
   * 根据名称获取Prompt模板
   */
  getPromptByName(name: string): PromptTemplate | undefined {
    return this.loadedPrompts.find(prompt => prompt.name === name);
  }

  /**
   * 停止文件监听
   */
  stopWatching(): void {
    if (!this.isWatching) {
      return;
    }

    for (const watcher of this.watchers) {
      watcher.close();
    }

    this.watchers = [];
    this.isWatching = false;
    this.emit('watching:stopped');
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.stopWatching();
    this.removeAllListeners();
    this.loadedPrompts = [];
  }
}
