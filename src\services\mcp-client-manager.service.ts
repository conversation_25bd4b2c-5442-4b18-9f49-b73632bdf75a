/**
 * MCP客户端管理服务
 * 用于连接和管理多个MCP服务器，并提供工具调用功能
 */

import { spawn, ChildProcess } from 'child_process';
import { EventEmitter } from 'events';
import type { ILogger } from './logger.service.js';
import type { MCPClientConfig, MCPServerConfig, MCPServerStatus } from '../types/mcp-client.js';

export interface MCPConnection {
  name: string;
  config: MCPServerConfig;
  process?: ChildProcess | undefined;
  status: 'disconnected' | 'connecting' | 'connected' | 'error';
  tools: MCPTool[];
  lastError?: string | undefined;
  connectedAt?: Date | undefined;
}

export interface MCPTool {
  name: string;
  description: string;
  inputSchema: Record<string, unknown>;
}

export interface MCPToolCallResult {
  success: boolean;
  result?: unknown;
  error?: string;
  executionTime: number;
}

export interface IMCPClientManager {
  loadConfig(configPath: string): Promise<void>;
  connectServer(serverName: string): Promise<boolean>;
  disconnectServer(serverName: string): Promise<boolean>;
  listConnections(): MCPConnection[];
  getServerTools(serverName: string): MCPTool[];
  callTool(
    serverName: string,
    toolName: string,
    args: Record<string, unknown>
  ): Promise<MCPToolCallResult>;
  getServerStatus(serverName: string): MCPServerStatus | null;
}

export class MCPClientManager extends EventEmitter implements IMCPClientManager {
  private logger: ILogger;
  private config?: MCPClientConfig;
  private connections = new Map<string, MCPConnection>();

  constructor(logger: ILogger) {
    super();
    this.logger = logger.forComponent('MCPClientManager');
  }

  /**
   * 加载MCP配置
   */
  async loadConfig(configPath: string): Promise<void> {
    try {
      const { MCPClientConfigService } = await import('./mcp-client-config.service.js');
      const configService = new MCPClientConfigService(this.logger);

      const result = await configService.parseConfig(configPath);

      if (!result.success || !result.config) {
        throw new Error(`Failed to load config: ${result.errors.map(e => e.message).join(', ')}`);
      }

      this.config = result.config;

      // 初始化连接对象
      for (const [name, serverConfig] of Object.entries(this.config.mcpServers)) {
        this.connections.set(name, {
          name,
          config: serverConfig,
          status: 'disconnected',
          tools: [],
        });
      }

      this.logger.info('MCP configuration loaded', {
        serverCount: Object.keys(this.config.mcpServers).length,
        configPath,
      });

      this.emit('config:loaded', this.config);
    } catch (error) {
      this.logger.error('Failed to load MCP configuration', error as Error, { configPath });
      throw error;
    }
  }

  /**
   * 连接到MCP服务器
   */
  async connectServer(serverName: string): Promise<boolean> {
    const connection = this.connections.get(serverName);
    if (!connection) {
      this.logger.warn('Server not found in configuration', { serverName });
      return false;
    }

    if (connection.status === 'connected') {
      this.logger.info('Server already connected', { serverName });
      return true;
    }

    try {
      connection.status = 'connecting';
      this.emit('server:connecting', serverName);

      // 启动MCP服务器进程
      const process = this.spawnMCPServer(connection.config);
      connection.process = process;

      // 设置进程事件监听
      this.setupProcessListeners(serverName, process);

      // 模拟连接成功（实际实现中需要MCP协议握手）
      await this.simulateConnection(connection);

      connection.status = 'connected';
      connection.connectedAt = new Date();

      this.logger.info('Successfully connected to MCP server', { serverName });
      this.emit('server:connected', serverName);

      return true;
    } catch (error) {
      connection.status = 'error';
      connection.lastError = error instanceof Error ? error.message : String(error);

      this.logger.error('Failed to connect to MCP server', error as Error, { serverName });
      this.emit('server:error', serverName, error);

      return false;
    }
  }

  /**
   * 断开MCP服务器连接
   */
  async disconnectServer(serverName: string): Promise<boolean> {
    const connection = this.connections.get(serverName);
    if (!connection) {
      return false;
    }

    try {
      if (connection.process) {
        connection.process.kill('SIGTERM');
        connection.process = undefined;
      }

      connection.status = 'disconnected';
      connection.tools = [];
      connection.lastError = undefined;
      connection.connectedAt = undefined;

      this.logger.info('Disconnected from MCP server', { serverName });
      this.emit('server:disconnected', serverName);

      return true;
    } catch (error) {
      this.logger.error('Failed to disconnect from MCP server', error as Error, { serverName });
      return false;
    }
  }

  /**
   * 列出所有连接
   */
  listConnections(): MCPConnection[] {
    return Array.from(this.connections.values());
  }

  /**
   * 获取服务器工具列表
   */
  getServerTools(serverName: string): MCPTool[] {
    const connection = this.connections.get(serverName);
    return connection?.tools || [];
  }

  /**
   * 调用MCP工具
   */
  async callTool(
    serverName: string,
    toolName: string,
    args: Record<string, unknown>
  ): Promise<MCPToolCallResult> {
    const startTime = Date.now();

    try {
      const connection = this.connections.get(serverName);
      if (!connection || connection.status !== 'connected') {
        throw new Error(`Server ${serverName} is not connected`);
      }

      // 检查工具是否存在
      const tool = connection.tools.find(t => t.name === toolName);
      if (!tool) {
        throw new Error(`Tool ${toolName} not found on server ${serverName}`);
      }

      // 模拟工具调用（实际实现中需要MCP协议通信）
      const result = await this.simulateToolCall(serverName, toolName, args);

      const executionTime = Date.now() - startTime;

      this.logger.info('Tool call completed', {
        serverName,
        toolName,
        executionTime,
        success: true,
      });

      return {
        success: true,
        result,
        executionTime,
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      this.logger.error('Tool call failed', error as Error, {
        serverName,
        toolName,
        executionTime,
      });

      return {
        success: false,
        error: errorMessage,
        executionTime,
      };
    }
  }

  /**
   * 获取服务器状态
   */
  getServerStatus(serverName: string): MCPServerStatus | null {
    const connection = this.connections.get(serverName);
    if (!connection) {
      return null;
    }

    return {
      name: serverName,
      status:
        connection.status === 'connected'
          ? 'running'
          : connection.status === 'error'
            ? 'error'
            : 'stopped',
      pid: connection.process?.pid,
      startTime: connection.connectedAt,
      lastCheck: new Date(),
      error: connection.lastError,
    };
  }

  /**
   * 启动MCP服务器进程
   */
  private spawnMCPServer(config: MCPServerConfig): ChildProcess {
    const childProcess = spawn(config.command, config.args, {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: { ...process.env, ...config.env },
      cwd: config.cwd,
    });

    return childProcess;
  }

  /**
   * 设置进程事件监听
   */
  private setupProcessListeners(serverName: string, process: ChildProcess): void {
    process.on('error', error => {
      this.logger.error('MCP server process error', error, { serverName });
      const connection = this.connections.get(serverName);
      if (connection) {
        connection.status = 'error';
        connection.lastError = error.message;
      }
      this.emit('server:error', serverName, error);
    });

    process.on('exit', (code, signal) => {
      this.logger.info('MCP server process exited', { serverName, code, signal });
      const connection = this.connections.get(serverName);
      if (connection) {
        connection.status = 'disconnected';
        connection.process = undefined;
      }
      this.emit('server:disconnected', serverName);
    });
  }

  /**
   * 模拟连接过程（实际实现中需要MCP协议握手）
   */
  private async simulateConnection(connection: MCPConnection): Promise<void> {
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 模拟获取工具列表
    connection.tools = this.getMockTools(connection.name);
  }

  /**
   * 模拟工具调用（实际实现中需要MCP协议通信）
   */
  private async simulateToolCall(
    serverName: string,
    toolName: string,
    args: Record<string, unknown>
  ): Promise<unknown> {
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      server: serverName,
      tool: toolName,
      args,
      result: `Mock result from ${serverName}.${toolName}`,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取模拟工具列表
   */
  private getMockTools(serverName: string): MCPTool[] {
    const commonTools: Record<string, MCPTool[]> = {
      'mcp-deepwiki': [
        {
          name: 'deepwiki_fetch',
          description: 'Fetch repository documentation',
          inputSchema: { type: 'object', properties: { url: { type: 'string' } } },
        },
      ],
      'playwright-mcp': [
        {
          name: 'browser_navigate',
          description: 'Navigate to a URL',
          inputSchema: { type: 'object', properties: { url: { type: 'string' } } },
        },
        {
          name: 'browser_click',
          description: 'Click on an element',
          inputSchema: { type: 'object', properties: { selector: { type: 'string' } } },
        },
      ],
      'text-editor': [
        {
          name: 'view',
          description: 'View file content',
          inputSchema: { type: 'object', properties: { path: { type: 'string' } } },
        },
        {
          name: 'str_replace',
          description: 'Replace text in file',
          inputSchema: {
            type: 'object',
            properties: {
              path: { type: 'string' },
              old_str: { type: 'string' },
              new_str: { type: 'string' },
            },
          },
        },
      ],
    };

    return (
      commonTools[serverName] || [
        {
          name: 'generic_tool',
          description: `Generic tool for ${serverName}`,
          inputSchema: { type: 'object', properties: {} },
        },
      ]
    );
  }
}
