/**
 * Markdown 解析器类型定义
 * 从 markdown-parser.ts 中拆分出来以减少文件大小
 */

/**
 * Markdown 章节接口
 */
export interface MarkdownSection {
  /** 章节标题 */
  title: string;
  /** 标题级别 (1-6) */
  level: number;
  /** 章节内容 */
  content: string;
  /** 子章节 */
  subsections: MarkdownSection[];
  /** 章节元数据 */
  metadata: Record<string, string>;
  /** 章节在原文中的行号范围 */
  lineRange: { start: number; end: number };
}

/**
 * 解析后的 Markdown 提示结构
 */
export interface ParsedMarkdownPrompt {
  /** 文档标题 */
  title: string;
  /** 章节列表 */
  sections: MarkdownSection[];
  /** 文档级元数据 */
  metadata: Record<string, string>;
  /** 提取的参数定义 */
  parameters: MarkdownParameter[];
  /** 文档统计信息 */
  statistics: {
    totalLines: number;
    totalSections: number;
    totalWords: number;
    complexity: 'low' | 'medium' | 'high';
  };
}

/**
 * Markdown 参数定义
 */
export interface MarkdownParameter {
  /** 参数名称 */
  name: string;
  /** 参数描述 */
  description: string;
  /** 参数类型 */
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  /** 是否必需 */
  required: boolean;
  /** 默认值 */
  defaultValue?: unknown;
  /** 示例值 */
  examples?: unknown[];
  /** 验证规则 */
  validation?: {
    pattern?: string;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
  };
}
