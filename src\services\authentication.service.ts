/**
 * 认证服务 - 提供token验证和访问控制
 */

import crypto from 'crypto';
import { EventEmitter } from 'eventemitter3';
import type { ILogger } from './logger.service.js';
import type { ServerConfig } from '../types/config.js';
import { TokenManager } from './token-manager.service.js';
import type {
  AuthToken,
  AuthenticationResult,
  AuthenticationOptions,
} from './authentication.types.js';

export class AuthenticationService extends EventEmitter {
  private logger: ILogger;
  private config: ServerConfig;
  private tokens = new Map<string, AuthToken>();
  private failedAttempts = new Map<string, { count: number; lastAttempt: Date }>();
  private options: AuthenticationOptions;
  private tokenManager: TokenManager;

  constructor(config: ServerConfig, logger: ILogger) {
    super();
    this.config = config;
    this.logger = logger.forComponent('AuthenticationService');
    this.tokenManager = new TokenManager();

    this.options = {
      enableTokenAuth: process.env['MCP_ENABLE_AUTH'] === 'true',
      enableRateLimit: true,
      maxAttempts: 5,
      lockoutDuration: 15 * 60 * 1000, // 15分钟
      tokenExpiration: 24 * 60 * 60 * 1000, // 24小时
      allowedOrigins: ['*'],
    };

    this.initializeTokens();
    this.startCleanupTimer();
  }

  /**
   * 初始化tokens
   */
  private initializeTokens(): void {
    // 从环境变量加载token
    const envToken = process.env['MCP_AUTH_TOKEN'];
    if (envToken) {
      const token: AuthToken = {
        token: envToken,
        name: 'Environment Token',
        permissions: ['*'],
        createdAt: new Date(),
        metadata: { source: 'environment' },
      };
      this.tokens.set(envToken, token);
      this.logger.info('Authentication token loaded from environment');
    }

    // 从配置文件加载tokens
    const authConfig = this.config.plugins?.config as Record<string, unknown>;
    const configTokens = (authConfig?.['auth'] as Record<string, unknown>)?.['tokens'] as
      | AuthToken[]
      | undefined;
    if (configTokens && Array.isArray(configTokens)) {
      configTokens.forEach(tokenConfig => {
        const token: AuthToken = {
          ...tokenConfig,
          createdAt: new Date(tokenConfig.createdAt),
          expiresAt: tokenConfig.expiresAt ? new Date(tokenConfig.expiresAt) : undefined,
          lastUsedAt: tokenConfig.lastUsedAt ? new Date(tokenConfig.lastUsedAt) : undefined,
        };
        this.tokens.set(token.token, token);
      });
      this.logger.info('Authentication tokens loaded from configuration', {
        count: configTokens.length,
      });
    }

    this.logger.info('Authentication service initialized', {
      tokenCount: this.tokens.size,
      authEnabled: this.options.enableTokenAuth,
      rateLimitEnabled: this.options.enableRateLimit,
    });
  }

  /**
   * 验证token
   */
  async authenticateToken(
    token: string,
    clientInfo?: { ip?: string; userAgent?: string }
  ): Promise<AuthenticationResult> {
    const clientId = clientInfo?.ip || 'unknown';

    try {
      // 检查速率限制
      if (this.options.enableRateLimit && !this.checkRateLimit(clientId)) {
        const attempts = this.failedAttempts.get(clientId);
        this.logger.warn('Authentication rate limit exceeded', {
          clientId,
          attempts: attempts?.count,
          lastAttempt: attempts?.lastAttempt,
        });

        return {
          success: false,
          error: 'Too many authentication attempts. Please try again later.',
          remainingAttempts: 0,
        };
      }

      // 如果未启用认证，允许所有请求
      if (!this.options.enableTokenAuth) {
        return { success: true };
      }

      // 验证token格式
      if (!token || typeof token !== 'string' || token.length < 10) {
        this.recordFailedAttempt(clientId);
        return {
          success: false,
          error: 'Invalid token format',
          remainingAttempts: this.getRemainingAttempts(clientId),
        };
      }

      // 首先尝试使用TokenManager验证
      const tokenVerification = await this.tokenManager.verifyToken(token);
      if (tokenVerification.valid && tokenVerification.token) {
        // 清除失败尝试记录
        this.failedAttempts.delete(clientId);

        // 转换为AuthToken格式
        const authToken: AuthToken = {
          token: tokenVerification.token.token,
          name: tokenVerification.token.name,
          permissions: tokenVerification.token.permissions,
          expiresAt: tokenVerification.token.expiresAt,
          createdAt: tokenVerification.token.createdAt,
          lastUsedAt: tokenVerification.token.lastUsedAt,
          metadata: tokenVerification.token.metadata,
        };

        this.logger.info('Authentication successful (managed token)', {
          tokenName: authToken.name,
          permissions: authToken.permissions,
          clientId,
          userAgent: clientInfo?.userAgent,
        });

        this.emit('auth:success', {
          token: authToken,
          clientInfo,
          timestamp: new Date(),
        });

        return {
          success: true,
          token: authToken,
        };
      }

      // 回退到传统token验证
      const authToken = this.tokens.get(token);
      if (!authToken) {
        this.recordFailedAttempt(clientId);
        this.logger.warn('Authentication failed: token not found', {
          tokenPrefix: `${token.substring(0, 8)}...`,
          clientId,
          reason: tokenVerification.reason || 'Token not found in both managed and legacy stores',
        });

        return {
          success: false,
          error: tokenVerification.reason || 'Invalid token',
          remainingAttempts: this.getRemainingAttempts(clientId),
        };
      }

      // 检查token是否过期
      if (authToken.expiresAt && authToken.expiresAt < new Date()) {
        this.recordFailedAttempt(clientId);
        this.logger.warn('Authentication failed: token expired', {
          tokenName: authToken.name,
          expiresAt: authToken.expiresAt,
          clientId,
        });

        return {
          success: false,
          error: 'Token has expired',
          remainingAttempts: this.getRemainingAttempts(clientId),
        };
      }

      // 更新最后使用时间
      authToken.lastUsedAt = new Date();

      // 清除失败尝试记录
      this.failedAttempts.delete(clientId);

      this.logger.info('Authentication successful', {
        tokenName: authToken.name,
        permissions: authToken.permissions,
        clientId,
        userAgent: clientInfo?.userAgent,
      });

      this.emit('auth:success', {
        token: authToken,
        clientInfo,
        timestamp: new Date(),
      });

      return {
        success: true,
        token: authToken,
      };
    } catch (error) {
      this.recordFailedAttempt(clientId);
      this.logger.error('Authentication error', error as Error, { clientId });

      return {
        success: false,
        error: 'Authentication service error',
        remainingAttempts: this.getRemainingAttempts(clientId),
      };
    }
  }

  /**
   * 检查权限
   */
  hasPermission(token: AuthToken, permission: string): boolean {
    if (!token.permissions) {
      return false;
    }

    // 通配符权限
    if (token.permissions.includes('*')) {
      return true;
    }

    // 精确匹配
    if (token.permissions.includes(permission)) {
      return true;
    }

    // 前缀匹配
    return token.permissions.some(p => {
      if (p.endsWith('*')) {
        const prefix = p.slice(0, -1);
        return permission.startsWith(prefix);
      }
      return false;
    });
  }

  /**
   * 生成新token
   */
  generateToken(name: string, permissions: string[], expirationHours?: number): AuthToken {
    const token = crypto.randomBytes(32).toString('hex');
    const authToken: AuthToken = {
      token,
      name,
      permissions,
      createdAt: new Date(),
      expiresAt: expirationHours
        ? new Date(Date.now() + expirationHours * 60 * 60 * 1000)
        : undefined,
      metadata: { generated: true },
    };

    this.tokens.set(token, authToken);

    this.logger.info('New token generated', {
      name,
      permissions,
      expiresAt: authToken.expiresAt,
      tokenPrefix: `${token.substring(0, 8)}...`,
    });

    this.emit('token:generated', authToken);

    return authToken;
  }

  /**
   * 撤销token
   */
  revokeToken(token: string): boolean {
    const authToken = this.tokens.get(token);
    if (authToken) {
      this.tokens.delete(token);
      this.logger.info('Token revoked', {
        name: authToken.name,
        tokenPrefix: `${token.substring(0, 8)}...`,
      });

      this.emit('token:revoked', authToken);
      return true;
    }
    return false;
  }

  /**
   * 获取所有tokens（不包含实际token值）
   */
  getTokens(): Omit<AuthToken, 'token'>[] {
    return Array.from(this.tokens.values()).map(token => ({
      name: token.name,
      permissions: token.permissions,
      createdAt: token.createdAt,
      expiresAt: token.expiresAt,
      lastUsedAt: token.lastUsedAt,
      metadata: token.metadata,
    }));
  }

  /**
   * 检查速率限制
   */
  private checkRateLimit(clientId: string): boolean {
    const attempts = this.failedAttempts.get(clientId);
    if (!attempts) {
      return true;
    }

    const now = new Date();
    const timeSinceLastAttempt = now.getTime() - attempts.lastAttempt.getTime();

    // 如果锁定期已过，重置计数
    if (timeSinceLastAttempt > this.options.lockoutDuration) {
      this.failedAttempts.delete(clientId);
      return true;
    }

    // 检查是否超过最大尝试次数
    return attempts.count < this.options.maxAttempts;
  }

  /**
   * 记录失败尝试
   */
  private recordFailedAttempt(clientId: string): void {
    const attempts = this.failedAttempts.get(clientId) || { count: 0, lastAttempt: new Date() };
    attempts.count++;
    attempts.lastAttempt = new Date();
    this.failedAttempts.set(clientId, attempts);

    this.emit('auth:failed', {
      clientId,
      attempts: attempts.count,
      timestamp: attempts.lastAttempt,
    });
  }

  /**
   * 获取剩余尝试次数
   */
  private getRemainingAttempts(clientId: string): number {
    const attempts = this.failedAttempts.get(clientId);
    if (!attempts) {
      return this.options.maxAttempts;
    }
    return Math.max(0, this.options.maxAttempts - attempts.count);
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    // 每小时清理一次过期token和失败尝试记录
    setInterval(
      () => {
        this.cleanupExpiredTokens();
        this.cleanupFailedAttempts();
      },
      60 * 60 * 1000
    );
  }

  /**
   * 清理过期tokens
   */
  private cleanupExpiredTokens(): void {
    const now = new Date();
    let cleanedCount = 0;

    for (const [token, authToken] of this.tokens.entries()) {
      if (authToken.expiresAt && authToken.expiresAt < now) {
        this.tokens.delete(token);
        cleanedCount++;

        this.logger.info('Expired token cleaned up', {
          name: authToken.name,
          expiresAt: authToken.expiresAt,
        });
      }
    }

    if (cleanedCount > 0) {
      this.logger.info('Token cleanup completed', { cleanedCount });
    }
  }

  /**
   * 清理过期的失败尝试记录
   */
  private cleanupFailedAttempts(): void {
    const now = new Date();
    let cleanedCount = 0;

    for (const [clientId, attempts] of this.failedAttempts.entries()) {
      const timeSinceLastAttempt = now.getTime() - attempts.lastAttempt.getTime();
      if (timeSinceLastAttempt > this.options.lockoutDuration) {
        this.failedAttempts.delete(clientId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.debug('Failed attempts cleanup completed', { cleanedCount });
    }
  }

  /**
   * 获取TokenManager实例
   */
  getTokenManager(): TokenManager {
    return this.tokenManager;
  }

  /**
   * 获取认证统计信息
   */
  getAuthStats(): {
    totalTokens: number;
    activeTokens: number;
    expiredTokens: number;
    failedAttempts: number;
    lockedClients: number;
  } {
    const now = new Date();
    let activeTokens = 0;
    let expiredTokens = 0;

    for (const token of this.tokens.values()) {
      if (token.expiresAt) {
        if (token.expiresAt > now) {
          activeTokens++;
        } else {
          expiredTokens++;
        }
      } else {
        activeTokens++; // 永不过期的token
      }
    }

    const lockedClients = Array.from(this.failedAttempts.values()).filter(
      attempts => attempts.count >= this.options.maxAttempts
    ).length;

    return {
      totalTokens: this.tokens.size,
      activeTokens,
      expiredTokens,
      failedAttempts: this.failedAttempts.size,
      lockedClients,
    };
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.tokens.clear();
    this.failedAttempts.clear();
    this.removeAllListeners();
  }
}
