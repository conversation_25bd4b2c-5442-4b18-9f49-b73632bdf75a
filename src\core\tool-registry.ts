/**
 * 工具注册器 - 负责将Prompt模板转换为MCP工具并管理工具生命周期
 */

import { EventEmitter } from 'eventemitter3';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import type { PromptTemplate, PromptArgument } from '../types/prompt.js';
import type { MCPToolResult } from '../types/mcp.js';
import type { ServerConfig } from '../types/config.js';

export interface IToolRegistry extends EventEmitter {
  registerTool(prompt: PromptTemplate): void;
  unregisterTool(name: string): void;
  getRegisteredTools(): string[];
  hasToolRegistered(name: string): boolean;
  clearAllTools(): void;
  destroy(): void;
}

export class ToolRegistry extends EventEmitter implements IToolRegistry {
  private server: Server;
  private registeredTools = new Set<string>();

  constructor(server: Server, _config: ServerConfig) {
    super();
    this.server = server;
  }

  /**
   * 将Prompt模板注册为MCP工具
   */
  registerTool(prompt: PromptTemplate): void {
    try {
      // 如果工具已存在，先注销
      if (this.registeredTools.has(prompt.name)) {
        this.unregisterTool(prompt.name);
      }

      // 构建工具的输入schema
      const inputSchema = this.buildInputSchema(prompt);

      // 注册工具 - 使用正确的类型断言
      (
        this.server as unknown as {
          tool: (
            name: string,
            description: string,
            schema: Record<string, unknown>,
            handler: (args: Record<string, unknown>) => Promise<unknown>
          ) => void;
        }
      ).tool(
        prompt.name,
        prompt.description || `Prompt: ${prompt.name}`,
        inputSchema,
        async (args: Record<string, unknown>) => {
          return this.handleToolCall(prompt, args);
        }
      );

      this.registeredTools.add(prompt.name);
      this.emit('tool:registered', prompt.name, prompt);
    } catch (error) {
      this.emit('tool:error', prompt.name, error);
      throw new Error(
        `Failed to register tool "${prompt.name}": ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * 构建工具的输入schema (JSON Schema格式)
   */
  private buildInputSchema(prompt: PromptTemplate): Record<string, unknown> {
    const properties: Record<string, unknown> = {};
    const required: string[] = [];

    if (prompt.arguments && Array.isArray(prompt.arguments)) {
      for (const arg of prompt.arguments) {
        properties[arg.name] = this.createJsonSchemaProperty(arg);
        if (arg.required) {
          required.push(arg.name);
        }
      }
    }

    return {
      type: 'object',
      properties,
      required,
      additionalProperties: false,
    };
  }

  /**
   * 根据参数定义创建JSON Schema属性
   */
  private createJsonSchemaProperty(arg: PromptArgument): Record<string, unknown> {
    const property: Record<string, unknown> = {};

    switch (arg.type) {
      case 'string':
        property['type'] = 'string';
        break;
      case 'number':
        property['type'] = 'number';
        break;
      case 'boolean':
        property['type'] = 'boolean';
        break;
      case 'array':
        property['type'] = 'array';
        property['items'] = { type: 'string' };
        break;
      case 'object':
        property['type'] = 'object';
        break;
      default:
        property['type'] = 'string';
    }

    // 添加描述
    if (arg.description) {
      property['description'] = arg.description;
    }

    // 添加默认值
    if (arg.default !== undefined) {
      property['default'] = arg.default;
    }

    return property;
  }

  /**
   * 处理工具调用
   */
  private async handleToolCall(
    prompt: PromptTemplate,
    args: Record<string, unknown>
  ): Promise<MCPToolResult> {
    try {
      this.emit('tool:called', prompt.name, args);

      // 处理prompt内容
      const processedContent = await this.processPromptContent(prompt, args);

      const result: MCPToolResult = {
        content: [
          {
            type: 'text',
            text: processedContent,
          },
        ],
      };

      this.emit('tool:success', prompt.name, args, result);
      return result;
    } catch (error) {
      this.emit('tool:error', prompt.name, args, error);

      return {
        content: [
          {
            type: 'text',
            text: `Error processing prompt "${prompt.name}": ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }

  /**
   * 处理Prompt内容，替换参数占位符
   */
  private async processPromptContent(
    prompt: PromptTemplate,
    args: Record<string, unknown>
  ): Promise<string> {
    let promptText = '';

    if (prompt.messages && Array.isArray(prompt.messages)) {
      // 只处理用户消息
      const userMessages = prompt.messages.filter(msg => msg.role === 'user');

      for (const message of userMessages) {
        if (message.content && typeof message.content.text === 'string') {
          let text = message.content.text;

          // 替换所有 {{arg}} 格式的参数
          for (const [key, value] of Object.entries(args)) {
            const placeholder = new RegExp(`{{${key}}}`, 'g');
            text = text.replace(placeholder, String(value));
          }

          // 检查条件渲染
          if (message.condition) {
            const shouldRender = await this.evaluateCondition(message.condition, args);
            if (!shouldRender) {
              continue;
            }
          }

          promptText += `${text}\n\n`;
        }
      }
    }

    return promptText.trim();
  }

  /**
   * 评估条件表达式 - 使用安全的表达式评估器
   */
  private async evaluateCondition(
    condition: string,
    args: Record<string, unknown>
  ): Promise<boolean> {
    // 使用安全的表达式评估器替代eval()
    const { SafeExpressionEvaluator } = await import('../utils/expression-evaluator.js');
    return SafeExpressionEvaluator.evaluateCondition(condition, args);
  }

  /**
   * 注销工具
   */
  unregisterTool(name: string): void {
    if (this.registeredTools.has(name)) {
      // MCP SDK 可能没有直接的注销方法，这里记录状态
      this.registeredTools.delete(name);
      this.emit('tool:unregistered', name);
    }
  }

  /**
   * 获取已注册的工具列表
   */
  getRegisteredTools(): string[] {
    return Array.from(this.registeredTools);
  }

  /**
   * 检查工具是否已注册
   */
  hasToolRegistered(name: string): boolean {
    return this.registeredTools.has(name);
  }

  /**
   * 清除所有工具
   */
  clearAllTools(): void {
    const tools = Array.from(this.registeredTools);
    for (const tool of tools) {
      this.unregisterTool(tool);
    }
    this.emit('tools:cleared');
  }

  /**
   * 获取工具统计信息
   */
  getStats(): { totalTools: number; registeredTools: string[] } {
    return {
      totalTools: this.registeredTools.size,
      registeredTools: this.getRegisteredTools(),
    };
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.clearAllTools();
    this.removeAllListeners();
  }
}
