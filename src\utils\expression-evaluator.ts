/**
 * 企业级安全表达式评估器和输入验证工具
 * 重构后的模块化导出，保持向后兼容性
 */

// 重新导出类型定义
export type {
  ExpressionContext,
  ExpressionVariable,
  ValidationOptions,
  NumberValidationOptions,
  ArrayValidationOptions,
  SecurityConfig,
  SecurityEvent,
} from './security/types.js';

// 重新导出主要类
export { SafeExpressionEvaluator } from './expression/evaluator.js';
export { InputValidator } from './validation/input-validator.js';

// 重新导出配置管理
export {
  SecurityConfigManager,
  getSecurityConfig,
  getSecurityPolicy,
  getCacheConfig,
  getPerformanceConfig,
} from './security/config.js';

// 重新导出安全模式
export {
  SECURITY_PATTERNS,
  EXPRESSION_PATTERNS,
  DANGEROUS_KEYWORDS,
  ALLOWED_KEYWORDS,
  containsDangerousPattern,
  containsDangerousKeyword,
  getCachedPattern,
} from './security/patterns.js';
