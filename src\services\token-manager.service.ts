/**
 * Token管理服务 - 提供完整的token生命周期管理
 */

import crypto from 'crypto';
import { promises as fs } from 'fs';
import { existsSync } from 'fs';
import path from 'path';
import { EventEmitter } from 'eventemitter3';
import { InputValidator } from '../utils/validation/input-validator';

export interface ManagedToken {
  token: string;
  name: string;
  permissions: string[];
  expiresAt?: Date | undefined;
  createdAt: Date;
  lastUsedAt?: Date | undefined;
  metadata?: Record<string, unknown> | undefined;
  revoked?: boolean | undefined;
  revokedAt?: Date | undefined;
}

export interface TokenCreateOptions {
  name: string;
  permissions: string[];
  expiresAt?: Date | undefined;
  metadata?: Record<string, unknown> | undefined;
}

export interface TokenVerificationResult {
  valid: boolean;
  token?: ManagedToken;
  reason?: string;
}

export class TokenManager extends EventEmitter {
  private tokens = new Map<string, ManagedToken>();
  private tokenStorePath: string;
  private initialized = false;

  constructor(tokenStorePath?: string) {
    super();
    this.tokenStorePath = tokenStorePath || path.join(process.cwd(), '.mcp-tokens.json');
  }

  /**
   * 初始化TokenManager（异步加载tokens）
   */
  async initialize(): Promise<void> {
    if (!this.initialized) {
      await this.loadTokens();
      this.initialized = true;
    }
  }

  /**
   * 确保已初始化
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  /**
   * 创建新的token
   */
  async createToken(options: TokenCreateOptions): Promise<ManagedToken> {
    await this.ensureInitialized();

    // 验证输入
    const validatedName = InputValidator.validateString(options.name, {
      minLength: 1,
      maxLength: 100,
      pattern: /^[a-zA-Z0-9._-]+$/,
    });

    const validatedPermissions = InputValidator.validatePermissions(options.permissions);

    // 生成安全的随机token
    const tokenBytes = crypto.randomBytes(32);
    const token = tokenBytes.toString('base64url');

    const managedToken: ManagedToken = {
      token,
      name: validatedName,
      permissions: validatedPermissions,
      expiresAt: options.expiresAt,
      createdAt: new Date(),
      metadata: options.metadata || {},
      revoked: false,
    };

    this.tokens.set(token, managedToken);
    await this.saveTokens();

    this.emit('token:created', managedToken);
    return managedToken;
  }

  /**
   * 验证token
   */
  async verifyToken(token: string): Promise<TokenVerificationResult> {
    await this.ensureInitialized();

    const managedToken = this.tokens.get(token);

    if (!managedToken) {
      return {
        valid: false,
        reason: 'Token not found',
      };
    }

    if (managedToken.revoked) {
      return {
        valid: false,
        reason: 'Token has been revoked',
      };
    }

    if (managedToken.expiresAt && managedToken.expiresAt < new Date()) {
      return {
        valid: false,
        reason: 'Token has expired',
      };
    }

    // 更新最后使用时间
    managedToken.lastUsedAt = new Date();
    await this.saveTokens();

    this.emit('token:used', managedToken);

    return {
      valid: true,
      token: managedToken,
    };
  }

  /**
   * 撤销token
   */
  async revokeToken(token: string): Promise<boolean> {
    await this.ensureInitialized();

    const managedToken = this.tokens.get(token);

    if (!managedToken) {
      return false;
    }

    managedToken.revoked = true;
    managedToken.revokedAt = new Date();
    await this.saveTokens();

    this.emit('token:revoked', managedToken);
    return true;
  }

  /**
   * 列出所有token
   */
  async listTokens(): Promise<ManagedToken[]> {
    await this.ensureInitialized();
    return Array.from(this.tokens.values());
  }

  /**
   * 清理过期的token
   */
  async cleanupExpiredTokens(): Promise<number> {
    await this.ensureInitialized();

    const now = new Date();
    let cleanedCount = 0;

    for (const [token, managedToken] of this.tokens.entries()) {
      if (managedToken.expiresAt && managedToken.expiresAt < now) {
        this.tokens.delete(token);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      await this.saveTokens();
      this.emit('tokens:cleaned', cleanedCount);
    }

    return cleanedCount;
  }

  /**
   * 检查权限
   */
  async hasPermission(token: string, permission: string): Promise<boolean> {
    await this.ensureInitialized();

    const managedToken = this.tokens.get(token);

    if (!managedToken || managedToken.revoked) {
      return false;
    }

    // 通配符权限
    if (managedToken.permissions.includes('*')) {
      return true;
    }

    // 精确匹配
    if (managedToken.permissions.includes(permission)) {
      return true;
    }

    // 模式匹配（例如：admin.* 匹配 admin.users）
    return managedToken.permissions.some(p => {
      if (p.endsWith('*')) {
        const prefix = p.slice(0, -1);
        return permission.startsWith(prefix);
      }
      return false;
    });
  }

  /**
   * 获取token统计信息
   */
  async getTokenStats(): Promise<{
    total: number;
    active: number;
    expired: number;
    revoked: number;
  }> {
    await this.ensureInitialized();

    const now = new Date();
    let active = 0;
    let expired = 0;
    let revoked = 0;

    for (const token of this.tokens.values()) {
      if (token.revoked) {
        revoked++;
      } else if (token.expiresAt && token.expiresAt < now) {
        expired++;
      } else {
        active++;
      }
    }

    return {
      total: this.tokens.size,
      active,
      expired,
      revoked,
    };
  }

  /**
   * 从文件加载tokens
   */
  private async loadTokens(): Promise<void> {
    try {
      if (!existsSync(this.tokenStorePath)) {
        return;
      }

      const data = await fs.readFile(this.tokenStorePath, 'utf8');
      const tokenData = JSON.parse(data);

      this.tokens.clear();
      for (const tokenInfo of tokenData.tokens || []) {
        // 转换日期字符串为Date对象
        const managedToken: ManagedToken = {
          ...tokenInfo,
          createdAt: new Date(tokenInfo.createdAt),
          expiresAt: tokenInfo.expiresAt ? new Date(tokenInfo.expiresAt) : undefined,
          lastUsedAt: tokenInfo.lastUsedAt ? new Date(tokenInfo.lastUsedAt) : undefined,
          revokedAt: tokenInfo.revokedAt ? new Date(tokenInfo.revokedAt) : undefined,
        };

        this.tokens.set(managedToken.token, managedToken);
      }
    } catch (error) {
      console.warn('Failed to load tokens:', error);
    }
  }

  /**
   * 保存tokens到文件
   */
  private async saveTokens(): Promise<void> {
    try {
      const tokenData = {
        version: '1.0.0',
        updatedAt: new Date().toISOString(),
        tokens: Array.from(this.tokens.values()),
      };

      await fs.writeFile(this.tokenStorePath, JSON.stringify(tokenData, null, 2), 'utf8');
    } catch (error) {
      console.error('Failed to save tokens:', error);
      throw error;
    }
  }

  /**
   * 导出tokens（用于备份）
   */
  async exportTokens(filePath: string): Promise<void> {
    await this.ensureInitialized();

    const tokenData = {
      version: '1.0.0',
      exportedAt: new Date().toISOString(),
      tokens: Array.from(this.tokens.values()),
    };

    await fs.writeFile(filePath, JSON.stringify(tokenData, null, 2), 'utf8');
  }

  /**
   * 导入tokens（用于恢复）
   */
  async importTokens(filePath: string, merge = false): Promise<number> {
    await this.ensureInitialized();

    const data = await fs.readFile(filePath, 'utf8');
    const tokenData = JSON.parse(data);

    if (!merge) {
      this.tokens.clear();
    }

    let importedCount = 0;
    for (const tokenInfo of tokenData.tokens || []) {
      const managedToken: ManagedToken = {
        ...tokenInfo,
        createdAt: new Date(tokenInfo.createdAt),
        expiresAt: tokenInfo.expiresAt ? new Date(tokenInfo.expiresAt) : undefined,
        lastUsedAt: tokenInfo.lastUsedAt ? new Date(tokenInfo.lastUsedAt) : undefined,
        revokedAt: tokenInfo.revokedAt ? new Date(tokenInfo.revokedAt) : undefined,
      };

      this.tokens.set(managedToken.token, managedToken);
      importedCount++;
    }

    await this.saveTokens();
    return importedCount;
  }
}
