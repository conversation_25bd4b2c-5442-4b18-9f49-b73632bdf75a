/**
 * 认证服务类型定义
 * 从 authentication.service.ts 中拆分出来以减少文件大小
 */

export interface AuthToken {
  token: string;
  name: string;
  permissions: string[];
  expiresAt?: Date | undefined;
  createdAt: Date;
  lastUsedAt?: Date | undefined;
  metadata?: Record<string, unknown> | undefined;
}

export interface AuthenticationResult {
  success: boolean;
  token?: AuthToken;
  error?: string;
  remainingAttempts?: number;
}

export interface AuthenticationOptions {
  enableTokenAuth: boolean;
  enableRateLimit: boolean;
  maxAttempts: number;
  lockoutDuration: number;
  tokenExpiration: number;
  allowedOrigins: string[];
}
