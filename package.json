{"name": "mcp-prompt-server", "version": "2.0.0", "description": "Production-ready MCP Prompt Server with TypeScript, plugin system, and enterprise-grade architecture", "main": "dist/index.js", "bin": {"mcp-prompt-server": "dist/bin/simple-cli.js"}, "type": "module", "engines": {"node": ">=18.0.0"}, "scripts": {"start": "node dist/index.js", "dev": "tsx watch src/index.ts", "build": "tsc && node scripts/copy-assets.js", "build:fast": "esbuild src/index.ts --bundle --platform=node --outfile=dist/index.js --format=esm --external:@modelcontextprotocol/sdk", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["mcp", "claude", "prompt", "ai", "typescript", "plugin-system", "modern"], "author": "", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "fs-extra": "^11.2.0", "yaml": "^2.3.4", "zod": "^3.22.4", "winston": "^3.11.0", "chokidar": "^3.5.3", "eventemitter3": "^5.0.1", "commander": "^11.1.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/fs-extra": "^11.0.4", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "esbuild": "^0.19.8", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0", "rimraf": "^5.0.5", "tsx": "^4.6.0", "typescript": "^5.3.0"}}