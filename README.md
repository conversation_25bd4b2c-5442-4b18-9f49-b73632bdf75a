# MCP Prompt Server

企业级 MCP（模型上下文协议）服务器，基于 TypeScript 构建，具备全面的安全防护和生产就绪架构。

## 项目概述

这是一个专业的 MCP 服务器，将您的提示模板转换为智能工具。采用企业级安全架构、性能优化和全面的验证系统构建。

### 核心优势

- **🚀 生产就绪**: 企业级架构，具备全面的安全防护
- **🔒 安全优先**: 高级输入验证、XSS 防护和注入攻击防护
- **⚡ 高性能**: 针对高并发环境优化，具备智能缓存
- **🛠️ 开发友好**: 完整的 TypeScript 支持和类型定义
- **📦 插件系统**: 可扩展架构，支持自定义提示模板

## 系统架构

```
├── src/
│   ├── core/           # 核心业务逻辑
│   ├── services/       # 服务层（认证、配置、日志）
│   ├── utils/          # 安全工具和验证器
│   ├── types/          # TypeScript 类型定义
│   └── prompts/        # 提示模板库
├── config/             # 配置文件
└── dist/              # 编译输出
```

## 功能特性

### 核心功能
- **📦 丰富的提示库**: 精选的高质量提示集合，涵盖 Web 开发、内容创作和业务分析
- **🛠️ MCP 工具集成**: 自动工具注册，具备参数验证和类型安全
- **🔄 热重载**: 无需重启服务器即可动态加载提示
- **🎯 智能路由**: 基于上下文和参数的智能提示选择

### 企业级安全
- **🔒 输入验证**: 全面验证所有输入类型（字符串、数字、数组、文件、URL）
- **🛡️ XSS 防护**: 高级 XSS 过滤，具备 HTML 清理和脚本注入防护
- **🚫 注入防护**: SQL 注入、命令注入和路径遍历防护
- **⚡ 性能限制**: DoS 防护，具备可配置的速率限制和资源约束

### 生产特性
- **📊 监控**: 内置健康检查、指标收集和性能监控
- **🔐 认证**: 基于令牌的认证和基于角色的访问控制
- **📝 日志**: 结构化日志，具备可配置级别和轮转
- **⚙️ 配置**: 基于环境的配置，具备验证和热重载

## 快速开始

### 安装部署

1. **克隆并安装依赖**
   ```bash
   git clone <repository-url>
   cd mcp-prompt-server
   npm install
   ```

2. **构建项目**
   ```bash
   npm run build
   ```

3. **启动服务器**
   ```bash
   npm start
   ```

### 配置设置

创建生产环境配置文件：

```bash
# 复制示例配置
cp config/default.json config/production.json

# 编辑配置以适应您的环境
nano config/production.json
```

### 环境变量

```bash
# 安全配置
SECURITY_STRICT_MODE=true
SECURITY_MAX_STRING_LENGTH=10000
SECURITY_MAX_EXPRESSION_LENGTH=1000

# 性能配置
PERFORMANCE_MAX_EXECUTION_TIME=5000
PERFORMANCE_ENABLE_MONITORING=true

# 服务器配置
NODE_ENV=production
PORT=3000
```

## MCP 客户端集成

### Claude Desktop

将以下配置添加到您的 Claude Desktop 配置文件 (`claude_desktop_config.json`)：

```json
{
  "mcpServers": {
    "prompt-server": {
      "command": "node",
      "args": ["/path/to/mcp-prompt-server/dist/index.js"],
      "env": {
        "NODE_ENV": "production"
      }
    }
  }
}
```

### Cursor IDE

编辑 `~/.cursor/mcp_config.json`：

```json
{
  "servers": [
    {
      "name": "Prompt Server",
      "command": "node",
      "args": ["/path/to/mcp-prompt-server/dist/index.js"],
      "transport": "stdio",
      "env": {
        "NODE_ENV": "production",
        "SECURITY_STRICT_MODE": "true"
      }
    }
  ]
}
```

### 其他 MCP 客户端

对于任何兼容 MCP 的客户端，使用：
- **命令**: `node`
- **参数**: `["/path/to/mcp-prompt-server/dist/index.js"]`
- **传输**: `stdio`
- **环境**: 设置 `NODE_ENV=production`

## 可用工具

服务器自动注册以下 MCP 工具：

### 核心工具
- **`prompts/get`** - 检索和处理带参数替换的提示模板
- **`prompts/list`** - 列出所有可用的提示模板
- **`prompts/reload`** - 无需重启即可热重载提示模板
- **`markdown/parse`** - 解析和分析 Markdown 内容，具备安全验证
- **`config/template`** - 为不同环境生成配置模板

### 安全特性
- **输入验证**: 所有参数都使用企业级验证器进行验证
- **XSS 防护**: HTML 内容经过清理以防止脚本注入
- **速率限制**: 可配置的请求限制以防止滥用
- **认证**: 基于令牌的认证确保安全访问

## 开发指南

### 添加自定义提示

1. 在 `src/prompts/` 中创建 YAML 文件：

```yaml
name: custom_prompt
description: "自定义提示描述"
arguments:
  - name: topic
    description: "讨论的主题"
    required: true
  - name: style
    description: "写作风格"
    required: false
messages:
  - role: user
    content:
      type: text
      text: |
        创建关于 {topic} 的内容，使用 {style} 风格。
```

2. 使用 `prompts/reload` 工具重新加载提示

### 配置管理

生产环境的环境变量：

```bash
# 安全设置
SECURITY_STRICT_MODE=true
SECURITY_MAX_STRING_LENGTH=10000
SECURITY_BLOCK_SUSPICIOUS_INPUT=true

# 性能设置
PERFORMANCE_MAX_EXECUTION_TIME=5000
PERFORMANCE_ENABLE_MONITORING=true

# 日志设置
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/mcp-prompt-server.log
```

## 生产部署

### Docker 部署

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist/ ./dist/
COPY config/ ./config/
COPY src/prompts/ ./src/prompts/
EXPOSE 3000
CMD ["npm", "start"]
```

### 健康监控

服务器包含内置的健康检查：
- **`/health`** - 基本健康状态
- **`/metrics`** - 性能指标
- **`/status`** - 详细系统状态

## 安全保障

本服务器实现了企业级安全：

- **输入验证**: 全面验证所有输入类型
- **XSS 防护**: 高级 HTML 清理和脚本过滤
- **注入防护**: SQL、命令和路径遍历防护
- **速率限制**: 可配置的请求节流
- **认证**: 基于令牌的访问控制
- **审计日志**: 完整的请求/响应日志记录

## 许可证

MIT 许可证 - 详情请参阅 LICENSE 文件。

## 技术支持

如需企业支持、自定义集成或安全问题咨询，请提交 issue 或联系维护者。

---

**注意**: 本项目专为企业级生产环境设计，具备完整的安全防护和性能优化。建议在部署前仔细阅读配置文档并进行安全评估。
