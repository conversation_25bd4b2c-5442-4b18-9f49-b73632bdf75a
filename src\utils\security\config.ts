/**
 * 企业级安全配置管理器
 * 支持环境变量配置，适配生产环境负载
 * 重构为模块化设计，符合500行限制
 */

import type { SecurityConfig, SecurityPolicy, CacheConfig, PerformanceConfig } from './types.js';
import {
  DEFAULT_SECURITY_CONFIG,
  DEFAULT_SECURITY_POLICY,
  DEFAULT_CACHE_CONFIG,
  DEFAULT_PERFORMANCE_CONFIG,
  validateDefaultConfigs,
} from './default-config.js';
import { ProductionLogger } from './environment-parser.js';

/**
 * 安全配置管理器
 * 提供配置的获取、验证和更新功能
 */
export class SecurityConfigManager {
  private static instance: SecurityConfigManager;
  private config: SecurityConfig;
  private policy: SecurityPolicy;
  private cacheConfig: CacheConfig;
  private performanceConfig: PerformanceConfig;
  private configChangeListeners: Array<(config: SecurityConfig) => void> = [];
  private lastConfigUpdate: Date;

  private constructor() {
    this.config = { ...DEFAULT_SECURITY_CONFIG };
    this.policy = { ...DEFAULT_SECURITY_POLICY };
    this.cacheConfig = { ...DEFAULT_CACHE_CONFIG };
    this.performanceConfig = { ...DEFAULT_PERFORMANCE_CONFIG };
    this.lastConfigUpdate = new Date();

    this.validateConfig();
    this.logConfigurationSummary();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): SecurityConfigManager {
    if (!SecurityConfigManager.instance) {
      SecurityConfigManager.instance = new SecurityConfigManager();
    }
    return SecurityConfigManager.instance;
  }

  /**
   * 获取安全配置
   */
  getSecurityConfig(): Readonly<SecurityConfig> {
    return { ...this.config };
  }

  /**
   * 获取安全策略
   */
  getSecurityPolicy(): Readonly<SecurityPolicy> {
    return { ...this.policy };
  }

  /**
   * 获取缓存配置
   */
  getCacheConfig(): Readonly<CacheConfig> {
    return { ...this.cacheConfig };
  }

  /**
   * 获取性能配置
   */
  getPerformanceConfig(): Readonly<PerformanceConfig> {
    return { ...this.performanceConfig };
  }

  /**
   * 更新安全配置
   */
  updateSecurityConfig(updates: Partial<SecurityConfig>): void {
    const newConfig = { ...this.config, ...updates };

    // 创建临时实例进行验证
    const tempManager = Object.create(SecurityConfigManager.prototype);
    tempManager.config = newConfig;
    tempManager.validateConfig();

    // 验证通过后应用配置
    this.config = newConfig;
    this.lastConfigUpdate = new Date();
    this.notifyConfigChange();
  }

  /**
   * 更新安全策略
   */
  updateSecurityPolicy(updates: Partial<SecurityPolicy>): void {
    this.policy = { ...this.policy, ...updates };
    this.lastConfigUpdate = new Date();
  }

  /**
   * 验证配置的有效性
   */
  private validateConfig(): void {
    // 验证默认配置
    validateDefaultConfigs();

    // 验证字符串长度配置
    if (this.config.maxStringLength < 1 || this.config.maxStringLength > 10000000) {
      throw new Error('Invalid maxStringLength: must be between 1 and 10,000,000');
    }

    // 验证数组长度配置
    if (this.config.maxArrayLength < 1 || this.config.maxArrayLength > 1000000) {
      throw new Error('Invalid maxArrayLength: must be between 1 and 1,000,000');
    }

    // 验证路径配置
    if (this.config.maxPathDepth < 1 || this.config.maxPathDepth > 100) {
      throw new Error('Invalid maxPathDepth: must be between 1 and 100');
    }

    // 验证性能配置
    if (this.config.maxProcessingTime < 100 || this.config.maxProcessingTime > 60000) {
      throw new Error('Invalid maxProcessingTime: must be between 100ms and 60s');
    }
  }

  /**
   * 重置为默认配置
   */
  resetToDefaults(): void {
    this.config = { ...DEFAULT_SECURITY_CONFIG };
    this.policy = { ...DEFAULT_SECURITY_POLICY };
    this.cacheConfig = { ...DEFAULT_CACHE_CONFIG };
    this.performanceConfig = { ...DEFAULT_PERFORMANCE_CONFIG };
    this.lastConfigUpdate = new Date();
  }

  /**
   * 获取配置摘要（用于日志记录）
   */
  getConfigSummary(): Record<string, unknown> {
    return {
      security: {
        maxStringLength: this.config.maxStringLength,
        maxArrayLength: this.config.maxArrayLength,
        maxExpressionLength: this.config.maxExpressionLength,
        strictMode: this.policy.strictMode,
      },
      performance: {
        maxProcessingTime: this.config.maxProcessingTime,
        enableMonitoring: this.performanceConfig.enableMonitoring,
      },
      cache: {
        enabled: this.cacheConfig.enabled,
        maxSize: this.cacheConfig.maxSize,
      },
      lastUpdate: this.lastConfigUpdate.toISOString(),
    };
  }

  /**
   * 添加配置变更监听器
   */
  addConfigChangeListener(listener: (config: SecurityConfig) => void): void {
    this.configChangeListeners.push(listener);
  }

  /**
   * 移除配置变更监听器
   */
  removeConfigChangeListener(listener: (config: SecurityConfig) => void): void {
    const index = this.configChangeListeners.indexOf(listener);
    if (index > -1) {
      this.configChangeListeners.splice(index, 1);
    }
  }

  /**
   * 通知配置变更
   */
  private notifyConfigChange(): void {
    this.configChangeListeners.forEach(listener => {
      try {
        listener(this.config);
      } catch (error) {
        ProductionLogger.error('Config change listener failed:', error);
      }
    });
  }

  /**
   * 记录配置摘要
   */
  private logConfigurationSummary(): void {
    const summary = this.getConfigSummary();
    ProductionLogger.log('Security configuration loaded:', summary);
  }

  /**
   * 从环境变量重新加载配置
   */
  reloadFromEnvironment(): void {
    const newConfig = { ...DEFAULT_SECURITY_CONFIG };
    const newPolicy = { ...DEFAULT_SECURITY_POLICY };
    const newCacheConfig = { ...DEFAULT_CACHE_CONFIG };
    const newPerformanceConfig = { ...DEFAULT_PERFORMANCE_CONFIG };

    // 验证新配置
    const tempManager = Object.create(SecurityConfigManager.prototype);
    tempManager.config = newConfig;
    tempManager.policy = newPolicy;
    tempManager.cacheConfig = newCacheConfig;
    tempManager.performanceConfig = newPerformanceConfig;

    try {
      tempManager.validateConfig();

      // 如果验证通过，应用新配置
      this.config = newConfig;
      this.policy = newPolicy;
      this.cacheConfig = newCacheConfig;
      this.performanceConfig = newPerformanceConfig;
      this.lastConfigUpdate = new Date();

      this.notifyConfigChange();
      this.logConfigurationSummary();

      ProductionLogger.log('Configuration reloaded from environment variables');
    } catch (error) {
      ProductionLogger.error('Failed to reload configuration:', error);
      throw error;
    }
  }

  /**
   * 导出配置为JSON
   */
  exportConfig(): string {
    return JSON.stringify(
      {
        security: this.config,
        policy: this.policy,
        cache: this.cacheConfig,
        performance: this.performanceConfig,
        metadata: {
          lastUpdate: this.lastConfigUpdate.toISOString(),
          version: '1.0.0',
        },
      },
      null,
      2
    );
  }

  /**
   * 验证配置文件格式
   */
  validateConfigFile(configJson: string): boolean {
    try {
      const parsed = JSON.parse(configJson);

      // 基本结构验证
      if (!parsed.security || !parsed.policy || !parsed.cache || !parsed.performance) {
        return false;
      }

      // 创建临时实例进行验证
      const tempManager = Object.create(SecurityConfigManager.prototype);
      tempManager.config = parsed.security;
      tempManager.policy = parsed.policy;
      tempManager.cacheConfig = parsed.cache;
      tempManager.performanceConfig = parsed.performance;

      tempManager.validateConfig();
      return true;
    } catch {
      return false;
    }
  }
}

/**
 * 便捷函数：获取当前安全配置
 */
export function getSecurityConfig(): Readonly<SecurityConfig> {
  return SecurityConfigManager.getInstance().getSecurityConfig();
}

/**
 * 便捷函数：获取当前安全策略
 */
export function getSecurityPolicy(): Readonly<SecurityPolicy> {
  return SecurityConfigManager.getInstance().getSecurityPolicy();
}

/**
 * 便捷函数：获取当前缓存配置
 */
export function getCacheConfig(): Readonly<CacheConfig> {
  return SecurityConfigManager.getInstance().getCacheConfig();
}

/**
 * 便捷函数：获取当前性能配置
 */
export function getPerformanceConfig(): Readonly<PerformanceConfig> {
  return SecurityConfigManager.getInstance().getPerformanceConfig();
}
