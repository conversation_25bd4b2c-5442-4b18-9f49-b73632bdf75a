#!/usr/bin/env node

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const projectRoot = path.resolve(__dirname, '..');
const srcDir = path.join(projectRoot, 'src');
const distDir = path.join(projectRoot, 'dist');

async function copyAssets() {
  try {
    console.log('Copying assets to dist directory...');
    
    // 确保dist目录存在
    await fs.ensureDir(distDir);
    
    // 复制prompts目录
    const promptsDir = path.join(srcDir, 'prompts');
    if (await fs.pathExists(promptsDir)) {
      await fs.copy(promptsDir, path.join(distDir, 'prompts'));
      console.log('✓ Copied prompts directory');
    }

    // 复制custom-prompts目录
    const customPromptsDir = path.join(projectRoot, 'custom-prompts');
    if (await fs.pathExists(customPromptsDir)) {
      await fs.copy(customPromptsDir, path.join(distDir, 'custom-prompts'));
      console.log('✓ Copied custom-prompts directory');
    }
    
    // 复制配置文件
    const configFiles = [
      'config',
      'templates',
      'assets'
    ];
    
    for (const configDir of configFiles) {
      const srcPath = path.join(srcDir, configDir);
      const distPath = path.join(distDir, configDir);
      
      if (await fs.pathExists(srcPath)) {
        await fs.copy(srcPath, distPath);
        console.log(`✓ Copied ${configDir} directory`);
      }
    }
    
    // 复制package.json（用于生产环境）
    const packageJson = await fs.readJson(path.join(projectRoot, 'package.json'));
    const prodPackageJson = {
      name: packageJson.name,
      version: packageJson.version,
      description: packageJson.description,
      main: packageJson.main,
      bin: packageJson.bin,
      type: packageJson.type,
      engines: packageJson.engines,
      keywords: packageJson.keywords,
      author: packageJson.author,
      license: packageJson.license,
      dependencies: packageJson.dependencies
    };

    await fs.writeJson(path.join(distDir, 'package.json'), prodPackageJson, { spaces: 2 });
    console.log('✓ Created production package.json');

    // 复制文档
    const docsDir = path.join(projectRoot, 'docs');
    if (await fs.pathExists(docsDir)) {
      await fs.copy(docsDir, path.join(distDir, 'docs'));
      console.log('✓ Copied docs directory');
    }

    // 复制配置示例
    const configDir = path.join(projectRoot, 'config');
    if (await fs.pathExists(configDir)) {
      await fs.copy(configDir, path.join(distDir, 'config'));
      console.log('✓ Copied config directory');
    }

    // 确保CLI文件有执行权限
    const cliPath = path.join(distDir, 'bin', 'cli.js');
    if (await fs.pathExists(cliPath)) {
      await fs.chmod(cliPath, '755');
      console.log('✓ Set CLI executable permissions');
    }
    
    console.log('Assets copied successfully!');
  } catch (error) {
    console.error('Error copying assets:', error);
    process.exit(1);
  }
}

copyAssets();
