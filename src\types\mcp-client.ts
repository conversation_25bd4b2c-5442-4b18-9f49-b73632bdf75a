/**
 * MCP客户端配置类型定义
 * 用于解析和管理MCP客户端配置文件
 */

/**
 * MCP服务器配置接口
 */
export interface MCPServerConfig {
  /** 启动命令 */
  command: string;
  /** 命令参数 */
  args: string[];
  /** 环境变量 */
  env?: Record<string, string> | undefined;
  /** 服务器描述 */
  description?: string | undefined;
  /** 工作目录 */
  cwd?: string | undefined;
  /** 传输方式 */
  transport?: 'stdio' | 'http' | 'sse' | undefined;
  /** HTTP/SSE配置 */
  url?: string | undefined;
  /** 超时配置 */
  timeout?: number | undefined;
  /** 重试配置 */
  retries?: number | undefined;
}

/**
 * MCP客户端配置接口
 */
export interface MCPClientConfig {
  /** MCP服务器配置映射 */
  mcpServers: Record<string, MCPServerConfig>;
  /** 全局设置 */
  globalSettings?:
    | {
        /** 默认超时时间 */
        timeout?: number | undefined;
        /** 默认重试次数 */
        retryAttempts?: number | undefined;
        /** 日志级别 */
        logLevel?: 'debug' | 'info' | 'warn' | 'error' | undefined;
        /** 并发限制 */
        maxConcurrentServers?: number | undefined;
      }
    | undefined;
}

/**
 * 配置解析结果
 */
export interface ConfigParseResult {
  /** 是否解析成功 */
  success: boolean;
  /** 解析后的配置 */
  config?: MCPClientConfig;
  /** 错误信息 */
  errors: ConfigParseError[];
  /** 警告信息 */
  warnings: ConfigParseWarning[];
}

/**
 * 配置解析错误
 */
export interface ConfigParseError {
  /** 错误路径 */
  path: string;
  /** 错误消息 */
  message: string;
  /** 错误值 */
  value?: unknown;
  /** 错误类型 */
  type: 'validation' | 'format' | 'missing' | 'invalid';
}

/**
 * 配置解析警告
 */
export interface ConfigParseWarning {
  /** 警告路径 */
  path: string;
  /** 警告消息 */
  message: string;
  /** 建议 */
  suggestion?: string;
}

/**
 * 服务器状态
 */
export interface MCPServerStatus {
  /** 服务器名称 */
  name: string;
  /** 运行状态 */
  status: 'running' | 'stopped' | 'error' | 'unknown';
  /** 进程ID */
  pid?: number | undefined;
  /** 启动时间 */
  startTime?: Date | undefined;
  /** 最后检查时间 */
  lastCheck: Date;
  /** 错误信息 */
  error?: string | undefined;
}

/**
 * 配置管理操作结果
 */
export interface ConfigManagementResult {
  /** 操作是否成功 */
  success: boolean;
  /** 结果消息 */
  message: string;
  /** 操作数据 */
  data?: unknown;
  /** 错误信息 */
  error?: string;
}
