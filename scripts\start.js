#!/usr/bin/env node

/**
 * 跨平台启动脚本
 * 自动检测环境并设置正确的路径配置
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, resolve, join } from 'path';
import { existsSync } from 'fs';
import { platform } from 'os';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class CrossPlatformLauncher {
  constructor() {
    this.projectRoot = this.findProjectRoot();
    this.platform = platform();
    this.isWindows = this.platform === 'win32';
    this.isDevelopment = process.env.NODE_ENV !== 'production';
  }

  /**
   * 查找项目根目录
   */
  findProjectRoot() {
    let currentDir = __dirname;
    
    // 向上查找包含 package.json 的目录
    while (currentDir !== dirname(currentDir)) {
      const packageJsonPath = join(currentDir, 'package.json');
      if (existsSync(packageJsonPath)) {
        return currentDir;
      }
      currentDir = dirname(currentDir);
    }
    
    // 如果没找到，使用脚本目录的父目录
    return dirname(__dirname);
  }

  /**
   * 设置环境变量
   */
  setupEnvironment() {
    // 设置项目根目录
    if (!process.env.PROJECT_ROOT) {
      process.env.PROJECT_ROOT = this.projectRoot;
    }

    // 设置 Prompt 目录
    if (!process.env.MCP_PROMPTS_DIRECTORIES) {
      const promptDirs = [
        './dist/prompts',
        './dist/custom-prompts',
        './src/prompts', 
        './custom-prompts'
      ];
      process.env.MCP_PROMPTS_DIRECTORIES = promptDirs.join(',');
    }

    // 设置日志配置
    if (!process.env.MCP_LOG_LEVEL) {
      process.env.MCP_LOG_LEVEL = this.isDevelopment ? 'debug' : 'info';
    }

    if (!process.env.MCP_LOG_FORMAT) {
      process.env.MCP_LOG_FORMAT = this.isDevelopment ? 'text' : 'json';
    }

    // 设置安全配置
    if (!process.env.SECURITY_STRICT_MODE) {
      process.env.SECURITY_STRICT_MODE = this.isDevelopment ? 'false' : 'true';
    }

    console.log(`🚀 Starting MCP Prompt Server`);
    console.log(`📁 Project Root: ${this.projectRoot}`);
    console.log(`🖥️  Platform: ${this.platform}`);
    console.log(`🔧 Environment: ${this.isDevelopment ? 'development' : 'production'}`);
    console.log(`📂 Prompt Directories: ${process.env.MCP_PROMPTS_DIRECTORIES}`);
  }

  /**
   * 获取主程序路径
   */
  getMainScript() {
    const distPath = join(this.projectRoot, 'dist', 'index.js');
    const srcPath = join(this.projectRoot, 'src', 'index.ts');

    if (existsSync(distPath)) {
      return distPath;
    } else if (existsSync(srcPath)) {
      // 开发模式，使用 tsx 运行 TypeScript
      return srcPath;
    } else {
      throw new Error('Cannot find main script. Please build the project first.');
    }
  }

  /**
   * 获取 Node.js 命令
   */
  getNodeCommand(mainScript) {
    if (mainScript.endsWith('.ts')) {
      // TypeScript 文件，使用 tsx
      return {
        command: 'npx',
        args: ['tsx', mainScript]
      };
    } else {
      // JavaScript 文件，直接使用 node
      return {
        command: 'node',
        args: [mainScript]
      };
    }
  }

  /**
   * 启动服务器
   */
  async start() {
    try {
      this.setupEnvironment();
      
      const mainScript = this.getMainScript();
      const { command, args } = this.getNodeCommand(mainScript);

      console.log(`🎯 Executing: ${command} ${args.join(' ')}`);
      console.log('─'.repeat(50));

      // 启动子进程
      const child = spawn(command, args, {
        cwd: this.projectRoot,
        stdio: 'inherit',
        env: process.env,
        shell: this.isWindows // Windows 需要 shell
      });

      // 处理进程事件
      child.on('error', (error) => {
        console.error('❌ Failed to start server:', error.message);
        process.exit(1);
      });

      child.on('exit', (code, signal) => {
        if (code !== 0) {
          console.error(`❌ Server exited with code ${code} (signal: ${signal})`);
          process.exit(code || 1);
        } else {
          console.log('✅ Server stopped gracefully');
        }
      });

      // 处理中断信号
      process.on('SIGINT', () => {
        console.log('\n🛑 Received SIGINT, stopping server...');
        child.kill('SIGINT');
      });

      process.on('SIGTERM', () => {
        console.log('\n🛑 Received SIGTERM, stopping server...');
        child.kill('SIGTERM');
      });

    } catch (error) {
      console.error('❌ Startup failed:', error.message);
      process.exit(1);
    }
  }
}

// 解析命令行参数
const args = process.argv.slice(2);
const showHelp = args.includes('--help') || args.includes('-h');

if (showHelp) {
  console.log(`
MCP Prompt Server - Cross-Platform Launcher

Usage: node scripts/start.js [options]

Options:
  --help, -h     Show this help message
  
Environment Variables:
  NODE_ENV                    Set environment (development/production)
  PROJECT_ROOT               Override project root directory
  MCP_PROMPTS_DIRECTORIES    Comma-separated list of prompt directories
  MCP_LOG_LEVEL              Log level (debug/info/warn/error)
  MCP_LOG_FORMAT             Log format (text/json)
  SECURITY_STRICT_MODE       Enable strict security mode (true/false)

Examples:
  node scripts/start.js                    # Start with auto-detected settings
  NODE_ENV=production node scripts/start.js # Start in production mode
  MCP_LOG_LEVEL=debug node scripts/start.js # Start with debug logging
`);
  process.exit(0);
}

// 启动服务器
const launcher = new CrossPlatformLauncher();
launcher.start().catch((error) => {
  console.error('❌ Launcher failed:', error);
  process.exit(1);
});
