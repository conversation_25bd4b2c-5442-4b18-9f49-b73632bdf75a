/**
 * 企业级 Markdown 提示文件解析器
 * 用于解析复杂的 Markdown 格式提示文件，如 PRP6+MCP+ARUX.md
 * 支持完整的 Markdown 语法解析、元数据提取、参数识别和内容结构化
 */

import type { PromptTemplate, PromptArgument } from '../types/prompt.js';
import { InputValidator } from './validation/input-validator.js';
import type {
  MarkdownSection,
  ParsedMarkdownPrompt,
  MarkdownParameter,
} from './markdown-parser.types.js';

/**
 * 企业级 Markdown 提示解析器
 */
export class MarkdownPromptParser {
  private static readonly MAX_SECTION_DEPTH = 6;
  private static readonly MAX_CONTENT_LENGTH = 1024 * 1024; // 1MB
  private static readonly PARAMETER_PATTERNS = {
    CURLY_BRACES: /\{([^}]+)\}/g,
    DOUBLE_CURLY: /\{\{([^}]+)\}\}/g,
    ANGLE_BRACKETS: /<([^>]+)>/g,
    SQUARE_BRACKETS: /\[([^\]]+)\]/g,
  };

  /**
   * 解析 Markdown 提示文件
   * @param content Markdown 文件内容
   * @param filename 文件名
   * @returns 解析后的提示模板
   */
  static parseMarkdownPrompt(content: string, filename: string): PromptTemplate {
    try {
      // 输入验证
      this.validateInput(content, filename);

      // 解析 Markdown 结构
      const parsed = this.parseMarkdownStructure(content);

      // 生成提示名称
      const name = this.generatePromptName(filename);

      // 提取描述
      const description = this.extractDescription(parsed);

      // 提取参数
      const arguments_ = this.extractArguments(parsed);

      // 构建消息
      const messages = this.buildMessages(parsed);

      return {
        name,
        description,
        arguments: arguments_,
        messages,
      };
    } catch (error) {
      throw new Error(
        `Markdown parsing failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * 验证输入参数
   */
  private static validateInput(content: string, filename: string): void {
    if (!content || typeof content !== 'string') {
      throw new Error('Content must be a non-empty string');
    }

    if (content.length > this.MAX_CONTENT_LENGTH) {
      throw new Error(`Content exceeds maximum length of ${this.MAX_CONTENT_LENGTH} characters`);
    }

    if (!filename || typeof filename !== 'string') {
      throw new Error('Filename must be a non-empty string');
    }

    // 验证文件名格式
    const validatedFilename = InputValidator.validateFilePath(filename);
    if (!validatedFilename) {
      throw new Error('Invalid filename format');
    }
  }

  /**
   * 解析 Markdown 结构
   */
  private static parseMarkdownStructure(content: string): ParsedMarkdownPrompt {
    const lines = content.split('\n');
    const sections: MarkdownSection[] = [];
    const metadata: Record<string, string> = {};
    const parameters: MarkdownParameter[] = [];

    let currentSection: MarkdownSection | null = null;
    let currentContent: string[] = [];
    let title = '';
    let currentLineNumber = 0;
    let sectionStartLine = 0;

    for (const line of lines) {
      currentLineNumber++;
      const trimmedLine = line.trim();

      // 检测标题
      const headerMatch = trimmedLine.match(/^(#{1,6})\s+(.+)$/);
      if (headerMatch && headerMatch[1] && headerMatch[2]) {
        // 保存当前部分
        if (currentSection) {
          currentSection.content = currentContent.join('\n').trim();
          currentSection.lineRange.end = currentLineNumber - 1;
          sections.push(currentSection);

          // 提取当前章节的参数
          const sectionParams = this.extractParametersFromContent(currentSection.content);
          parameters.push(...sectionParams);
        }

        const level = headerMatch[1].length;
        const headerTitle = headerMatch[2];

        // 验证标题级别
        if (level > this.MAX_SECTION_DEPTH) {
          throw new Error(
            `Section depth exceeds maximum allowed depth of ${this.MAX_SECTION_DEPTH}`
          );
        }

        // 如果是第一级标题，设为主标题
        if (level === 1 && !title) {
          title = headerTitle;
        }

        sectionStartLine = currentLineNumber;
        currentSection = {
          title: headerTitle,
          level,
          content: '',
          subsections: [],
          metadata: {},
          lineRange: { start: sectionStartLine, end: 0 },
        };
        currentContent = [];
      } else {
        // 添加到当前内容
        currentContent.push(line);
      }
    }

    // 保存最后一个部分
    if (currentSection) {
      currentSection.content = currentContent.join('\n').trim();
      currentSection.lineRange.end = currentLineNumber;
      sections.push(currentSection);

      // 提取最后一个章节的参数
      const sectionParams = this.extractParametersFromContent(currentSection.content);
      parameters.push(...sectionParams);
    }

    // 计算统计信息
    const statistics = this.calculateStatistics(content, sections);

    return {
      title: title || 'Untitled Prompt',
      sections,
      metadata,
      parameters,
      statistics,
    };
  }

  /**
   * 从文件名生成提示名称
   */
  private static generatePromptName(filename: string): string {
    const baseName = filename.replace(/\.[^/.]+$/, ''); // 移除扩展名
    return baseName.toLowerCase().replace(/[^a-z0-9]+/g, '_');
  }

  /**
   * 提取描述
   */
  private static extractDescription(parsed: ParsedMarkdownPrompt): string {
    // 查找角色定位或第一个有意义的段落
    const roleSection = parsed.sections.find(
      s => s.title.includes('角色') || s.title.includes('Role') || s.title.includes('定位')
    );

    if (roleSection && roleSection.content) {
      // 取前200个字符作为描述
      const description = roleSection.content.replace(/\n+/g, ' ').trim();
      return description.length > 200 ? `${description.substring(0, 200)}...` : description;
    }

    // 如果没有角色部分，使用第一个有内容的部分
    const firstContentSection = parsed.sections.find(s => s.content && s.content.length > 20);
    if (firstContentSection) {
      const description = firstContentSection.content.replace(/\n+/g, ' ').trim();
      return description.length > 200 ? `${description.substring(0, 200)}...` : description;
    }

    return `复杂的 AI 助手提示词：${parsed.title}`;
  }

  /**
   * 从内容中提取参数
   */
  private static extractParametersFromContent(content: string): MarkdownParameter[] {
    const parameters: MarkdownParameter[] = [];
    const parameterNames = new Set<string>();

    // 使用多种模式匹配参数
    for (const [patternName, pattern] of Object.entries(this.PARAMETER_PATTERNS)) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const paramName = match[1]?.trim();

        // 避免重复参数
        if (paramName && !parameterNames.has(paramName) && this.isValidParameterName(paramName)) {
          parameterNames.add(paramName);

          parameters.push({
            name: paramName,
            description: `参数：${paramName}（从${patternName}模式提取）`,
            type: this.inferParameterType(paramName, content),
            required: this.isParameterRequired(paramName, content),
            examples: this.extractParameterExamples(paramName, content),
          });
        }
      }

      // 重置正则表达式的lastIndex
      pattern.lastIndex = 0;
    }

    return parameters;
  }

  /**
   * 计算文档统计信息
   */
  private static calculateStatistics(
    content: string,
    sections: MarkdownSection[]
  ): {
    totalLines: number;
    totalSections: number;
    totalWords: number;
    complexity: 'low' | 'medium' | 'high';
  } {
    const lines = content.split('\n');
    const words = content.split(/\s+/).filter(word => word.length > 0);

    let complexity: 'low' | 'medium' | 'high' = 'low';

    // 基于多个因素计算复杂度
    const complexityScore =
      sections.length * 2 +
      words.length / 100 +
      lines.length / 50 +
      this.countNestedSections(sections) * 3;

    if (complexityScore > 50) {
      complexity = 'high';
    } else if (complexityScore > 20) {
      complexity = 'medium';
    }

    return {
      totalLines: lines.length,
      totalSections: sections.length,
      totalWords: words.length,
      complexity,
    };
  }

  /**
   * 计算嵌套章节数量
   */
  private static countNestedSections(sections: MarkdownSection[]): number {
    let count = 0;
    for (const section of sections) {
      if (section.level > 2) {
        count++;
      }
      count += this.countNestedSections(section.subsections);
    }
    return count;
  }

  /**
   * 验证参数名称是否有效
   */
  private static isValidParameterName(name: string): boolean {
    // 排除常见的非参数文本
    const invalidPatterns = [
      /^https?:\/\//i, // URLs
      /^[0-9]+$/, // 纯数字
      /^[^a-zA-Z]/, // 不以字母开头
      /\s{2,}/, // 多个空格
    ];

    return (
      name.length > 0 && name.length < 50 && !invalidPatterns.some(pattern => pattern.test(name))
    );
  }

  /**
   * 推断参数类型
   */
  private static inferParameterType(
    name: string,
    _context: string
  ): 'string' | 'number' | 'boolean' | 'array' | 'object' {
    const lowerName = name.toLowerCase();

    // 基于名称推断类型
    if (lowerName.includes('count') || lowerName.includes('number') || lowerName.includes('id')) {
      return 'number';
    }

    if (lowerName.includes('enable') || lowerName.includes('is') || lowerName.includes('has')) {
      return 'boolean';
    }

    if (lowerName.includes('list') || lowerName.includes('array') || lowerName.includes('items')) {
      return 'array';
    }

    if (
      lowerName.includes('config') ||
      lowerName.includes('settings') ||
      lowerName.includes('options')
    ) {
      return 'object';
    }

    return 'string';
  }

  /**
   * 判断参数是否必需
   */
  private static isParameterRequired(name: string, context: string): boolean {
    // 在上下文中查找必需性指示
    const requiredPatterns = [
      new RegExp(`${name}.*必需`, 'i'),
      new RegExp(`${name}.*required`, 'i'),
      new RegExp(`${name}.*必须`, 'i'),
    ];

    return requiredPatterns.some(pattern => pattern.test(context));
  }

  /**
   * 提取参数示例
   */
  private static extractParameterExamples(name: string, context: string): unknown[] {
    const examples: unknown[] = [];

    // 查找示例模式
    const examplePatterns = [
      new RegExp(`${name}.*示例[：:]\\s*([^\\n]+)`, 'i'),
      new RegExp(`${name}.*example[：:]\\s*([^\\n]+)`, 'i'),
      new RegExp(`${name}.*如[：:]\\s*([^\\n]+)`, 'i'),
    ];

    for (const pattern of examplePatterns) {
      const match = context.match(pattern);
      if (match && match[1]) {
        examples.push(match[1].trim());
      }
    }

    return examples;
  }

  /**
   * 提取参数（兼容旧接口）
   */
  private static extractArguments(parsed: ParsedMarkdownPrompt): PromptArgument[] {
    return parsed.parameters.map(param => {
      const result: PromptArgument = {
        name: param.name,
        description: param.description,
        type: param.type,
        required: param.required,
      };

      if (param.defaultValue !== undefined) {
        result.default = param.defaultValue;
      }

      if (param.validation?.pattern) {
        result.validation = [
          {
            type: 'pattern' as const,
            value: param.validation.pattern,
            message: `Invalid ${param.name} format`,
          },
        ];
      }

      return result;
    });
  }

  /**
   * 构建消息
   */
  private static buildMessages(parsed: ParsedMarkdownPrompt): Array<{
    role: 'user' | 'assistant' | 'system';
    content: {
      type: 'text';
      text: string;
    };
  }> {
    // 将整个 Markdown 内容作为一个系统消息
    const fullContent = this.reconstructMarkdown(parsed);

    return [
      {
        role: 'system',
        content: {
          type: 'text',
          text: fullContent,
        },
      },
    ];
  }

  /**
   * 重构 Markdown 内容
   */
  private static reconstructMarkdown(parsed: ParsedMarkdownPrompt): string {
    const parts: string[] = [];

    // 添加主标题
    if (parsed.title && parsed.title !== 'Untitled Prompt') {
      parts.push(`# ${parsed.title}\n`);
    }

    // 添加所有部分
    for (const section of parsed.sections) {
      const headerPrefix = '#'.repeat(section.level);
      parts.push(`${headerPrefix} ${section.title}\n`);

      if (section.content) {
        parts.push(`${section.content}\n`);
      }
    }

    return parts.join('\n');
  }

  /**
   * 验证 Markdown 提示格式
   */
  static validateMarkdownPrompt(content: string): boolean {
    // 基本验证：检查是否包含标题
    const hasHeaders = /^#{1,6}\s+.+$/m.test(content);

    // 检查内容长度
    const hasContent = content.trim().length > 100;

    return hasHeaders && hasContent;
  }
}
