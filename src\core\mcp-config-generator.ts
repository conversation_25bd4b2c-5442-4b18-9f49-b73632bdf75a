/**
 * MCP配置生成器 - 生成Augment兼容的MCP配置
 * 完全可配置，无硬编码实现
 */

import type { MCPServerConfig } from '../types/mcp-client.js';
import type { ILogger } from '../services/logger.service.js';
import type { IMCPClientConfigService } from '../services/mcp-client-config.service.js';
import type { IConfigService } from '../services/config.service.js';
import type { IUnifiedToolRegistry, ToolDefinition, ToolHandler } from './unified-tool-registry.js';

/**
 * MCP服务器工具估算配置接口
 */
export interface MCPServerToolEstimate {
  name: string;
  toolCount: number;
  displayName: string;
  patterns: string[];
}

/**
 * MCP配置生成器配置接口
 */
export interface MCPConfigGeneratorConfig {
  defaultConfigPath: string;
  defaultFormat: 'settings.json' | 'display' | 'both';
  augmentConfigKey: string;
  defaultToolCount: number;
  serverEstimates: MCPServerToolEstimate[];
  toolRegistration: {
    name: string;
    description: string;
    defaultConfigPath: string;
    defaultFormat: 'settings.json' | 'display' | 'both';
  };
}

export interface IMCPConfigGenerator {
  generateAugmentConfig(
    configPath: string,
    format: 'settings.json' | 'display' | 'both'
  ): Promise<Record<string, unknown>>;
  registerTool(unifiedRegistry: IUnifiedToolRegistry): void;
  updateConfig(config: Partial<MCPConfigGeneratorConfig>): void;
}

export class MCPConfigGenerator implements IMCPConfigGenerator {
  private readonly REGISTRAR_NAME = 'MCPConfigGenerator';
  private config: MCPConfigGeneratorConfig;

  constructor(
    private logger: ILogger,
    private mcpClientConfigService: IMCPClientConfigService,
    private configService: IConfigService
  ) {
    this.config = this.loadDefaultConfig();
  }

  /**
   * 加载默认配置
   */
  private loadDefaultConfig(): MCPConfigGeneratorConfig {
    const defaultConfig: MCPConfigGeneratorConfig = {
      defaultConfigPath:
        this.configService.get<string>('mcp.defaultConfigPath') || './mcp/mcp.json',
      defaultFormat: (this.configService.get<string>('mcp.defaultFormat') || 'both') as
        | 'settings.json'
        | 'display'
        | 'both',
      augmentConfigKey:
        this.configService.get<string>('mcp.augmentConfigKey') || 'augment.advanced',
      defaultToolCount: this.configService.get<number>('mcp.defaultToolCount') || 5,
      serverEstimates: this.configService.get<MCPServerToolEstimate[]>('mcp.serverEstimates') || [
        {
          name: 'github',
          toolCount: this.configService.get<number>('mcp.tools.github.count') || 15,
          displayName: this.configService.get<string>('mcp.tools.github.displayName') || 'GitHub',
          patterns: this.configService.get<string[]>('mcp.tools.github.patterns') || [
            'github',
            '@smithery-ai/github',
          ],
        },
        {
          name: 'playwright',
          toolCount: this.configService.get<number>('mcp.tools.playwright.count') || 24,
          displayName:
            this.configService.get<string>('mcp.tools.playwright.displayName') || 'Playwright',
          patterns: this.configService.get<string[]>('mcp.tools.playwright.patterns') || [
            'playwright',
            '@modelcontextprotocol/server-playwright',
          ],
        },
        {
          name: 'context7',
          toolCount: this.configService.get<number>('mcp.tools.context7.count') || 2,
          displayName:
            this.configService.get<string>('mcp.tools.context7.displayName') || 'Context 7',
          patterns: this.configService.get<string[]>('mcp.tools.context7.patterns') || [
            'context7',
            'context-7',
          ],
        },
        {
          name: 'sequential-thinking',
          toolCount: this.configService.get<number>('mcp.tools.sequentialThinking.count') || 1,
          displayName:
            this.configService.get<string>('mcp.tools.sequentialThinking.displayName') ||
            'Sequential thinking',
          patterns: this.configService.get<string[]>('mcp.tools.sequentialThinking.patterns') || [
            'sequential-thinking',
            'sequential_thinking',
          ],
        },
        {
          name: 'text-editor',
          toolCount: this.configService.get<number>('mcp.tools.textEditor.count') || 6,
          displayName:
            this.configService.get<string>('mcp.tools.textEditor.displayName') || 'text-editor',
          patterns: this.configService.get<string[]>('mcp.tools.textEditor.patterns') || [
            'text-editor',
            'text_editor',
          ],
        },
        {
          name: 'codebase-mcp',
          toolCount: this.configService.get<number>('mcp.tools.codebaseMcp.count') || 3,
          displayName:
            this.configService.get<string>('mcp.tools.codebaseMcp.displayName') || 'Codebase MCP',
          patterns: this.configService.get<string[]>('mcp.tools.codebaseMcp.patterns') || [
            'codebase-mcp',
            'codebase_mcp',
            'codebase',
          ],
        },
        {
          name: 'desktop-commander',
          toolCount: this.configService.get<number>('mcp.tools.desktopCommander.count') || 21,
          displayName:
            this.configService.get<string>('mcp.tools.desktopCommander.displayName') ||
            'desktop-commander',
          patterns: this.configService.get<string[]>('mcp.tools.desktopCommander.patterns') || [
            'desktop-commander',
            'desktop_commander',
          ],
        },
        {
          name: 'mcp-deepwiki',
          toolCount: this.configService.get<number>('mcp.tools.mcpDeepwiki.count') || 1,
          displayName:
            this.configService.get<string>('mcp.tools.mcpDeepwiki.displayName') || 'mcp-deepwiki',
          patterns: this.configService.get<string[]>('mcp.tools.mcpDeepwiki.patterns') || [
            'mcp-deepwiki',
            'deepwiki',
          ],
        },
        {
          name: 'mcp-taskmanager',
          toolCount: this.configService.get<number>('mcp.tools.mcpTaskmanager.count') || 10,
          displayName:
            this.configService.get<string>('mcp.tools.mcpTaskmanager.displayName') ||
            'mcp-taskmanager',
          patterns: this.configService.get<string[]>('mcp.tools.mcpTaskmanager.patterns') || [
            'mcp-taskmanager',
            'taskmanager',
          ],
        },
        {
          name: 'postman',
          toolCount: this.configService.get<number>('mcp.tools.postman.count') || 38,
          displayName:
            this.configService.get<string>('mcp.tools.postman.displayName') ||
            'postman-weaviate-mcp-server',
          patterns: this.configService.get<string[]>('mcp.tools.postman.patterns') || [
            'postman',
            'weaviate',
          ],
        },
        {
          name: 'windows-mcp',
          toolCount: this.configService.get<number>('mcp.tools.windowsMcp.count') || 14,
          displayName:
            this.configService.get<string>('mcp.tools.windowsMcp.displayName') || 'windows-mcp',
          patterns: this.configService.get<string[]>('mcp.tools.windowsMcp.patterns') || [
            'windows-mcp',
            'windows_mcp',
          ],
        },
      ],
      toolRegistration: {
        name:
          this.configService.get<string>('mcp.toolRegistration.name') ||
          'mcp_generate_augment_config',
        description:
          this.configService.get<string>('mcp.toolRegistration.description') ||
          '生成Augment兼容的MCP配置',
        defaultConfigPath:
          this.configService.get<string>('mcp.toolRegistration.defaultConfigPath') ||
          './mcp/mcp.json',
        defaultFormat: (this.configService.get<string>('mcp.toolRegistration.defaultFormat') ||
          'both') as 'settings.json' | 'display' | 'both',
      },
    };

    return defaultConfig;
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<MCPConfigGeneratorConfig>): void {
    this.config = { ...this.config, ...config };
    this.logger.debug('MCP Config Generator configuration updated', { config });
  }

  /**
   * 生成Augment兼容的MCP配置
   */
  async generateAugmentConfig(
    configPath: string,
    format: 'settings.json' | 'display' | 'both' = 'both'
  ): Promise<Record<string, unknown>> {
    try {
      // 解析配置文件
      const parseResult = await this.mcpClientConfigService.parseConfig(configPath);

      if (!parseResult.success || !parseResult.config) {
        throw new Error(
          `Failed to parse config: ${parseResult.errors.map(e => e.message).join(', ')}`
        );
      }

      const config = parseResult.config;
      const servers = Object.entries(config.mcpServers);

      // 生成settings.json格式
      const settingsJsonConfig = {
        [this.config.augmentConfigKey]: {
          mcpServers: servers.map(([name, serverConfig]) => ({
            name,
            command: serverConfig.command,
            args: serverConfig.args,
            ...(serverConfig.env &&
              Object.keys(serverConfig.env).length > 0 && {
                env: serverConfig.env,
              }),
            ...(serverConfig.cwd && { cwd: serverConfig.cwd }),
            ...(serverConfig.description && { description: serverConfig.description }),
          })),
        },
      };

      // 生成界面显示格式
      const displayConfig = {
        totalServers: servers.length,
        servers: servers.map(([name, serverConfig]) => {
          // 计算工具数量（模拟）
          const toolCount = this.estimateToolCount(name, serverConfig);

          return {
            name,
            displayName: this.getDisplayName(name, serverConfig),
            command: `${serverConfig.command} ${serverConfig.args.join(' ')}`,
            toolCount,
            status: 'configured',
            description: serverConfig.description || `${name} MCP Server`,
            hasEnv: !!(serverConfig.env && Object.keys(serverConfig.env).length > 0),
            envCount: serverConfig.env ? Object.keys(serverConfig.env).length : 0,
          };
        }),
        configSummary: {
          hasGlobalSettings: !!config.globalSettings,
          totalToolsEstimate: servers.reduce(
            (sum, [name, serverConfig]) => sum + this.estimateToolCount(name, serverConfig),
            0
          ),
          configuredServers: servers.length,
          serversWithEnv: servers.filter(
            ([, serverConfig]) => serverConfig.env && Object.keys(serverConfig.env).length > 0
          ).length,
        },
      };

      // 根据format参数返回相应结果
      const result: Record<string, unknown> = {
        success: true,
        configPath,
        format,
      };

      if (format === 'settings.json' || format === 'both') {
        result['settingsJson'] = settingsJsonConfig;
        result['settingsJsonString'] = JSON.stringify(settingsJsonConfig, null, 2);
      }

      if (format === 'display' || format === 'both') {
        result['displayConfig'] = displayConfig;
      }

      if (format === 'both') {
        result['instructions'] = {
          settingsJson: '将 settingsJsonString 内容复制到 VS Code 的 settings.json 文件中',
          display: 'displayConfig 包含了适合在界面中显示的格式化信息',
          usage: '重启 VS Code 后，这些 MCP 服务器将在 Augment Agent 中可用',
        };
      }

      return result;
    } catch (error) {
      this.logger.error('Failed to generate Augment config', error as Error, {
        configPath,
        format,
      });
      throw new Error(
        `Failed to generate Augment config: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * 估算MCP服务器的工具数量（基于配置的动态估算）
   */
  private estimateToolCount(name: string, config: MCPServerConfig): number {
    // 首先检查配置中的服务器估算
    for (const estimate of this.config.serverEstimates) {
      // 检查名称匹配
      if (estimate.patterns.some(pattern => name.toLowerCase().includes(pattern.toLowerCase()))) {
        return estimate.toolCount;
      }
    }

    // 基于命令估算
    const command = config.command.toLowerCase();
    for (const estimate of this.config.serverEstimates) {
      if (estimate.patterns.some(pattern => command.includes(pattern.toLowerCase()))) {
        return estimate.toolCount;
      }
    }

    // 返回默认估算
    return this.config.defaultToolCount;
  }

  /**
   * 获取显示名称（基于配置的动态获取）
   */
  private getDisplayName(name: string, config: MCPServerConfig): string {
    if (config.description) {
      return config.description;
    }

    // 从配置中查找显示名称
    for (const estimate of this.config.serverEstimates) {
      if (estimate.patterns.some(pattern => name.toLowerCase().includes(pattern.toLowerCase()))) {
        return estimate.displayName;
      }
    }

    // 如果没有找到匹配的配置，返回原名称
    return name;
  }

  /**
   * 生成MCP工具注册代码（基于配置的动态注册）
   */
  registerTool(unifiedRegistry: IUnifiedToolRegistry): void {
    try {
      const toolConfig = this.config.toolRegistration;

      // 创建工具定义
      const toolDefinition: ToolDefinition = {
        name: toolConfig.name,
        description: toolConfig.description,
        inputSchema: {
          type: 'object',
          properties: {
            configPath: {
              type: 'string',
              description: '配置文件路径',
              default: toolConfig.defaultConfigPath,
            },
            format: {
              type: 'string',
              enum: ['settings.json', 'display', 'both'],
              description: '输出格式：settings.json配置、界面显示格式或两者',
              default: toolConfig.defaultFormat,
            },
          },
          additionalProperties: false,
        },
      };

      // 创建工具处理器
      const toolHandler: ToolHandler = async (name: string, args: Record<string, unknown>) => {
        return this.handleToolCall(name, args);
      };

      // 注册工具到统一注册表
      unifiedRegistry.registerTool(toolDefinition, toolHandler, this.REGISTRAR_NAME);

      this.logger.info('MCP Config Generator tool registered successfully');
    } catch (error) {
      this.logger.error('Failed to register MCP Config Generator tool', error as Error);
      throw new Error(`MCP Config Generator tool registration failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 处理工具调用
   */
  private async handleToolCall(
    name: string,
    args: Record<string, unknown>
  ): Promise<{
    content: Array<{
      type: 'text';
      text: string;
    }>;
    isError?: boolean;
  }> {
    const toolConfig = this.config.toolRegistration;

    if (name === toolConfig.name) {
      return this.handleGenerateAugmentConfig(args);
    }

    throw new Error(`Unknown tool: ${name}`);
  }

  /**
   * 处理生成Augment配置工具调用
   */
  private async handleGenerateAugmentConfig(args: Record<string, unknown>): Promise<{
    content: Array<{
      type: 'text';
      text: string;
    }>;
  }> {
    const toolConfig = this.config.toolRegistration;
    const { configPath = toolConfig.defaultConfigPath, format = toolConfig.defaultFormat } =
      args as {
        configPath?: string;
        format?: 'settings.json' | 'display' | 'both';
      };

    try {
      const result = await this.generateAugmentConfig(configPath, format);

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      this.logger.error('Failed to generate Augment config via tool', error as Error, {
        configPath,
        format,
      });
      throw new Error(
        `Failed to generate Augment config: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * 生成Augment配置的完整示例（基于配置的动态示例）
   */
  generateConfigExample(): Record<string, unknown> {
    // 从配置中获取示例服务器
    const exampleServers = this.config.serverEstimates.slice(0, 3).map(estimate => {
      const serverName = estimate.name;

      // 根据服务器类型生成示例配置
      switch (serverName) {
        case 'github':
          return {
            name: serverName,
            command: this.configService.get<string>('mcp.examples.github.command') || 'cmd',
            args: this.configService.get<string[]>('mcp.examples.github.args') || [
              '/c',
              'npx',
              '-y',
              '@smithery/cli@latest',
              'run',
              '@smithery-ai/github',
              '--key',
              'your-key',
              '--profile',
              'your-profile',
            ],
          };
        case 'playwright':
          return {
            name: serverName,
            command: this.configService.get<string>('mcp.examples.playwright.command') || 'npx',
            args: this.configService.get<string[]>('mcp.examples.playwright.args') || [
              '-y',
              '@modelcontextprotocol/server-playwright@latest',
            ],
          };
        case 'text-editor':
          return {
            name: serverName,
            command: this.configService.get<string>('mcp.examples.textEditor.command') || 'node',
            args: this.configService.get<string[]>('mcp.examples.textEditor.args') || [
              'path/to/text-editor-mcp/build/text-editor.js',
            ],
          };
        default:
          return {
            name: serverName,
            command: this.configService.get<string>(`mcp.examples.${serverName}.command`) || 'node',
            args: this.configService.get<string[]>(`mcp.examples.${serverName}.args`) || [
              'server.js',
            ],
          };
      }
    });

    const exampleConfig = {
      [this.config.augmentConfigKey]: {
        mcpServers: exampleServers,
      },
    };

    const instructions = this.configService.get<Record<string, string>>(
      'mcp.examples.instructions'
    ) || {
      step1: '将上述配置复制到 VS Code 的 settings.json 文件中',
      step2: '根据实际情况修改服务器配置（命令路径、参数等）',
      step3: '重启 VS Code',
      step4: '在 Augment Agent 中验证 MCP 服务器是否正常工作',
    };

    const notes = this.configService.get<string[]>('mcp.examples.notes') || [
      '确保所有命令路径都是正确的',
      '某些服务器可能需要额外的环境变量',
      '检查服务器是否需要特定的依赖项',
      '可以通过 Augment 界面查看服务器连接状态',
    ];

    return {
      success: true,
      example: exampleConfig,
      exampleString: JSON.stringify(exampleConfig, null, 2),
      instructions,
      notes,
      configKey: this.config.augmentConfigKey,
      totalServers: exampleServers.length,
    };
  }
}
