/**
 * 企业级性能监控器
 * 提供执行时间监控、内存使用监控、性能分析等功能
 */

import type { PerformanceConfig } from './types.js';
import { getPerformanceConfig } from './config.js';
import { ProductionLogger } from './environment-parser.js';

/**
 * 性能指标接口
 */
interface PerformanceMetrics {
  executionTime: number;
  memoryUsage: number;
  cpuUsage?: number;
  timestamp: number;
}

/**
 * 性能统计信息
 */
interface PerformanceStats {
  totalOperations: number;
  averageExecutionTime: number;
  maxExecutionTime: number;
  minExecutionTime: number;
  slowOperations: number;
  memoryPeakUsage: number;
  averageMemoryUsage: number;
}

/**
 * 性能监控器类
 */
export class PerformanceMonitor {
  private config: PerformanceConfig;
  private metrics: PerformanceMetrics[] = [];
  private stats: PerformanceStats = {
    totalOperations: 0,
    averageExecutionTime: 0,
    maxExecutionTime: 0,
    minExecutionTime: Infinity,
    slowOperations: 0,
    memoryPeakUsage: 0,
    averageMemoryUsage: 0,
  };

  constructor(customConfig?: Partial<PerformanceConfig>) {
    this.config = { ...getPerformanceConfig(), ...customConfig };
  }

  /**
   * 开始性能监控
   */
  startMonitoring(operationName: string): PerformanceTracker | NoOpTracker {
    if (!this.config.enableMonitoring) {
      return new NoOpTracker();
    }

    return new PerformanceTracker(operationName, this);
  }

  /**
   * 记录性能指标
   */
  recordMetrics(operationName: string, metrics: PerformanceMetrics): void {
    if (!this.config.enableMonitoring) {
      return;
    }

    this.metrics.push(metrics);
    this.updateStats(metrics);

    // 检查是否为慢操作
    if (metrics.executionTime > this.config.maxExecutionTime) {
      this.handleSlowOperation(operationName, metrics);
    }

    // 检查内存使用
    if (metrics.memoryUsage > this.config.memoryThreshold) {
      this.handleHighMemoryUsage(operationName, metrics);
    }

    // 限制指标数组大小
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-500); // 保留最近500条记录
    }
  }

  /**
   * 获取性能统计信息
   */
  getStats(): PerformanceStats {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.metrics = [];
    this.stats = {
      totalOperations: 0,
      averageExecutionTime: 0,
      maxExecutionTime: 0,
      minExecutionTime: Infinity,
      slowOperations: 0,
      memoryPeakUsage: 0,
      averageMemoryUsage: 0,
    };
  }

  /**
   * 获取最近的性能指标
   */
  getRecentMetrics(count: number = 10): PerformanceMetrics[] {
    return this.metrics.slice(-count);
  }

  /**
   * 更新统计信息
   */
  private updateStats(metrics: PerformanceMetrics): void {
    this.stats.totalOperations++;

    // 更新执行时间统计
    this.stats.maxExecutionTime = Math.max(this.stats.maxExecutionTime, metrics.executionTime);
    this.stats.minExecutionTime = Math.min(this.stats.minExecutionTime, metrics.executionTime);

    // 计算平均执行时间
    this.stats.averageExecutionTime =
      (this.stats.averageExecutionTime * (this.stats.totalOperations - 1) + metrics.executionTime) /
      this.stats.totalOperations;

    // 更新内存使用统计
    this.stats.memoryPeakUsage = Math.max(this.stats.memoryPeakUsage, metrics.memoryUsage);
    this.stats.averageMemoryUsage =
      (this.stats.averageMemoryUsage * (this.stats.totalOperations - 1) + metrics.memoryUsage) /
      this.stats.totalOperations;
  }

  /**
   * 处理慢操作
   */
  private handleSlowOperation(operationName: string, metrics: PerformanceMetrics): void {
    this.stats.slowOperations++;

    if (this.config.logSlowOperations) {
      ProductionLogger.warn(
        `Slow operation detected: ${operationName} took ${metrics.executionTime}ms`,
        {
          operation: operationName,
          executionTime: metrics.executionTime,
          memoryUsage: metrics.memoryUsage,
          threshold: this.config.maxExecutionTime,
        }
      );
    }
  }

  /**
   * 处理高内存使用
   */
  private handleHighMemoryUsage(operationName: string, metrics: PerformanceMetrics): void {
    ProductionLogger.warn(
      `High memory usage detected: ${operationName} used ${Math.round(metrics.memoryUsage / 1024 / 1024)}MB`,
      {
        operation: operationName,
        memoryUsage: metrics.memoryUsage,
        threshold: this.config.memoryThreshold,
      }
    );
  }
}

/**
 * 性能跟踪器类
 */
export class PerformanceTracker {
  private startTime: number;
  private startMemory: number;
  private operationName: string;
  private monitor: PerformanceMonitor;

  constructor(operationName: string, monitor: PerformanceMonitor) {
    this.operationName = operationName;
    this.monitor = monitor;
    this.startTime = Date.now();
    this.startMemory = this.getMemoryUsage();
  }

  /**
   * 结束监控并记录指标
   */
  end(): PerformanceMetrics {
    const endTime = Date.now();
    const endMemory = this.getMemoryUsage();

    const metrics: PerformanceMetrics = {
      executionTime: endTime - this.startTime,
      memoryUsage: endMemory - this.startMemory,
      timestamp: endTime,
    };

    this.monitor.recordMetrics(this.operationName, metrics);
    return metrics;
  }

  /**
   * 获取当前内存使用量
   */
  private getMemoryUsage(): number {
    try {
      return process.memoryUsage().heapUsed;
    } catch {
      return 0;
    }
  }
}

/**
 * 空操作跟踪器（当监控被禁用时使用）
 */
class NoOpTracker implements Pick<PerformanceTracker, 'end'> {
  end(): PerformanceMetrics {
    return {
      executionTime: 0,
      memoryUsage: 0,
      timestamp: Date.now(),
    };
  }
}

/**
 * 全局性能监控器实例
 */
export const globalPerformanceMonitor = new PerformanceMonitor();

/**
 * 性能监控装饰器
 */
export function monitor(operationName?: string) {
  return function (target: unknown, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const name = operationName || `${target?.constructor?.name || 'Unknown'}.${propertyKey}`;

    descriptor.value = function (...args: unknown[]) {
      const tracker = globalPerformanceMonitor.startMonitoring(name);

      try {
        const result = originalMethod.apply(this, args);

        // 处理异步方法
        if (result && typeof result.then === 'function') {
          return result.finally(() => tracker.end());
        }

        tracker.end();
        return result;
      } catch (error) {
        tracker.end();
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * 异步性能监控函数
 */
export async function monitorAsync<T>(
  operationName: string,
  operation: () => Promise<T>
): Promise<T> {
  const tracker = globalPerformanceMonitor.startMonitoring(operationName);

  try {
    const result = await operation();
    tracker.end();
    return result;
  } catch (error) {
    tracker.end();
    throw error;
  }
}

/**
 * 同步性能监控函数
 */
export function monitorSync<T>(operationName: string, operation: () => T): T {
  const tracker = globalPerformanceMonitor.startMonitoring(operationName);

  try {
    const result = operation();
    tracker.end();
    return result;
  } catch (error) {
    tracker.end();
    throw error;
  }
}
