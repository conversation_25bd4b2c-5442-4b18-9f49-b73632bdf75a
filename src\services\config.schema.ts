/**
 * 配置验证Schema定义
 * 从config.service.ts中拆分出来以减少文件大小
 */

import { z } from 'zod';

// 配置验证Schema
export const ServerConfigSchema = z.object({
  server: z.object({
    name: z.string(),
    version: z.string(),
    description: z.string().optional(),
    port: z.number().optional(),
    promptsAsTools: z.boolean().optional(),
  }),
  prompts: z.object({
    directories: z.array(z.string()),
    watchForChanges: z.boolean(),
    cacheEnabled: z.boolean(),
    supportedFormats: z.array(z.string()),
    maxFileSize: z.number(),
  }),
  logging: z.object({
    level: z.enum(['debug', 'info', 'warn', 'error']),
    format: z.enum(['json', 'text']),
    file: z.string().optional(),
    console: z.boolean(),
  }),
  plugins: z.object({
    enabled: z.array(z.string()),
    config: z.record(z.unknown()),
    autoLoad: z.boolean(),
  }),
  performance: z.object({
    maxConcurrentTools: z.number(),
    requestTimeout: z.number(),
    cacheSize: z.number(),
    maxExecutionTime: z.number().optional(),
    enableMonitoring: z.boolean().optional(),
    memoryThreshold: z.number().optional(),
  }),
  security: z
    .object({
      strictMode: z.boolean(),
      maxStringLength: z.number(),
      maxArrayLength: z.number(),
      maxExpressionLength: z.number(),
      blockSuspiciousInput: z.boolean(),
      enableRateLimiting: z.boolean(),
      maxRequestsPerMinute: z.number(),
    })
    .optional(),
});

// 导出类型
export type ServerConfigType = z.infer<typeof ServerConfigSchema>;
