/**
 * 企业级审计日志系统
 * 提供安全事件记录、审计跟踪、合规性日志等功能
 */

import type { SecurityEvent } from './types.js';
import { ProductionLogger } from './environment-parser.js';

/**
 * 审计事件级别
 */
export enum AuditLevel {
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  CRITICAL = 'critical',
}

/**
 * 审计事件接口
 */
export interface AuditEvent {
  id: string;
  timestamp: Date;
  level: AuditLevel;
  category: string;
  action: string;
  resource?: string;
  userId?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  details: Record<string, unknown>;
  risk: 'low' | 'medium' | 'high' | 'critical';
  compliance?: string[];
}

/**
 * 审计日志配置
 */
interface AuditConfig {
  enabled: boolean;
  logLevel: AuditLevel;
  maxLogSize: number;
  retentionDays: number;
  encryptLogs: boolean;
  remoteLogging: boolean;
  complianceMode: boolean;
}

/**
 * 企业级审计日志器
 */
export class AuditLogger {
  private static instance: AuditLogger;
  private config: AuditConfig;
  private eventBuffer: AuditEvent[] = [];
  private eventCounter = 0;

  private constructor() {
    this.config = {
      enabled: process.env['AUDIT_ENABLED'] !== 'false',
      logLevel: (process.env['AUDIT_LOG_LEVEL'] as AuditLevel) || AuditLevel.INFO,
      maxLogSize: parseInt(process.env['AUDIT_MAX_LOG_SIZE'] || '10000', 10),
      retentionDays: parseInt(process.env['AUDIT_RETENTION_DAYS'] || '90', 10),
      encryptLogs: process.env['AUDIT_ENCRYPT_LOGS'] === 'true',
      remoteLogging: process.env['AUDIT_REMOTE_LOGGING'] === 'true',
      complianceMode: process.env['AUDIT_COMPLIANCE_MODE'] === 'true',
    };
  }

  /**
   * 获取单例实例
   */
  static getInstance(): AuditLogger {
    if (!AuditLogger.instance) {
      AuditLogger.instance = new AuditLogger();
    }
    return AuditLogger.instance;
  }

  /**
   * 记录审计事件
   */
  logEvent(event: Omit<AuditEvent, 'id' | 'timestamp'>): void {
    if (!this.config.enabled) {
      return;
    }

    const auditEvent: AuditEvent = {
      id: this.generateEventId(),
      timestamp: new Date(),
      ...event,
    };

    // 检查日志级别
    if (!this.shouldLog(auditEvent.level)) {
      return;
    }

    // 添加到缓冲区
    this.eventBuffer.push(auditEvent);

    // 立即记录高风险事件
    if (auditEvent.risk === 'critical' || auditEvent.risk === 'high') {
      this.flushEvent(auditEvent);
    }

    // 检查缓冲区大小
    if (this.eventBuffer.length >= 100) {
      this.flushBuffer();
    }
  }

  /**
   * 记录安全事件
   */
  logSecurityEvent(securityEvent: SecurityEvent): void {
    const auditEvent: Omit<AuditEvent, 'id' | 'timestamp'> = {
      level: this.mapSeverityToLevel(securityEvent.severity),
      category: 'security',
      action: securityEvent.type,
      details: {
        message: securityEvent.message,
        input: this.sanitizeForLogging(securityEvent.input),
        context: securityEvent.context,
      },
      risk:
        securityEvent.severity === 'critical'
          ? 'critical'
          : securityEvent.severity === 'high'
            ? 'high'
            : securityEvent.severity === 'medium'
              ? 'medium'
              : 'low',
      compliance: ['SOX', 'GDPR', 'HIPAA'], // 根据需要调整
    };

    this.logEvent(auditEvent);
  }

  /**
   * 记录访问事件
   */
  logAccess(action: string, resource: string, userId?: string, success: boolean = true): void {
    const auditEvent: Omit<AuditEvent, 'id' | 'timestamp'> = {
      level: success ? AuditLevel.INFO : AuditLevel.WARN,
      category: 'access',
      action,
      resource,
      details: {
        success,
        timestamp: new Date().toISOString(),
      },
      risk: success ? 'low' : 'medium',
    };

    if (userId) {
      auditEvent.userId = userId;
    }

    this.logEvent(auditEvent);
  }

  /**
   * 记录数据操作事件
   */
  logDataOperation(
    operation: string,
    resource: string,
    userId?: string,
    details?: Record<string, unknown>
  ): void {
    const auditEvent: Omit<AuditEvent, 'id' | 'timestamp'> = {
      level: AuditLevel.INFO,
      category: 'data',
      action: operation,
      resource,
      details: {
        operation,
        ...details,
      },
      risk: 'low',
      compliance: ['GDPR', 'CCPA'],
    };

    if (userId) {
      auditEvent.userId = userId;
    }

    this.logEvent(auditEvent);
  }

  /**
   * 记录配置变更事件
   */
  logConfigChange(setting: string, oldValue: unknown, newValue: unknown, userId?: string): void {
    const auditEvent: Omit<AuditEvent, 'id' | 'timestamp'> = {
      level: AuditLevel.WARN,
      category: 'configuration',
      action: 'config_change',
      resource: setting,
      details: {
        setting,
        oldValue: this.sanitizeForLogging(oldValue),
        newValue: this.sanitizeForLogging(newValue),
      },
      risk: 'medium',
      compliance: ['SOX'],
    };

    if (userId) {
      auditEvent.userId = userId;
    }

    this.logEvent(auditEvent);
  }

  /**
   * 记录错误事件
   */
  logError(error: Error, context?: Record<string, unknown>): void {
    this.logEvent({
      level: AuditLevel.ERROR,
      category: 'error',
      action: 'error_occurred',
      details: {
        errorName: error.name,
        errorMessage: this.sanitizeErrorMessage(error.message),
        stack: this.config.complianceMode ? '[REDACTED]' : error.stack,
        context: this.sanitizeForLogging(context),
      },
      risk: 'medium',
    });
  }

  /**
   * 刷新事件缓冲区
   */
  flushBuffer(): void {
    if (this.eventBuffer.length === 0) {
      return;
    }

    const events = [...this.eventBuffer];
    this.eventBuffer = [];

    events.forEach(event => this.flushEvent(event));
  }

  /**
   * 获取审计统计信息
   */
  getAuditStats(): Record<string, unknown> {
    return {
      totalEvents: this.eventCounter,
      bufferSize: this.eventBuffer.length,
      config: {
        enabled: this.config.enabled,
        logLevel: this.config.logLevel,
        complianceMode: this.config.complianceMode,
      },
    };
  }

  /**
   * 生成事件ID
   */
  private generateEventId(): string {
    this.eventCounter++;
    const timestamp = Date.now().toString(36);
    const counter = this.eventCounter.toString(36);
    return `audit_${timestamp}_${counter}`;
  }

  /**
   * 检查是否应该记录该级别的事件
   */
  private shouldLog(level: AuditLevel): boolean {
    const levels = [AuditLevel.INFO, AuditLevel.WARN, AuditLevel.ERROR, AuditLevel.CRITICAL];
    const currentLevelIndex = levels.indexOf(this.config.logLevel);
    const eventLevelIndex = levels.indexOf(level);
    return eventLevelIndex >= currentLevelIndex;
  }

  /**
   * 映射安全事件严重性到审计级别
   */
  private mapSeverityToLevel(severity: 'low' | 'medium' | 'high' | 'critical'): AuditLevel {
    switch (severity) {
      case 'critical':
        return AuditLevel.CRITICAL;
      case 'high':
        return AuditLevel.ERROR;
      case 'medium':
        return AuditLevel.WARN;
      case 'low':
      default:
        return AuditLevel.INFO;
    }
  }

  /**
   * 清理敏感信息用于日志记录
   */
  private sanitizeForLogging(data: unknown): unknown {
    if (typeof data === 'string') {
      // 移除潜在的敏感信息
      return data
        .replace(/password[=:]\s*\S+/gi, 'password=[REDACTED]')
        .replace(/token[=:]\s*\S+/gi, 'token=[REDACTED]')
        .replace(/key[=:]\s*\S+/gi, 'key=[REDACTED]')
        .replace(/secret[=:]\s*\S+/gi, 'secret=[REDACTED]');
    }

    if (typeof data === 'object' && data !== null) {
      const sanitized: Record<string, unknown> = {};
      for (const [key, value] of Object.entries(data)) {
        if (
          ['password', 'token', 'key', 'secret'].some(sensitive =>
            key.toLowerCase().includes(sensitive)
          )
        ) {
          sanitized[key] = '[REDACTED]';
        } else {
          sanitized[key] = this.sanitizeForLogging(value);
        }
      }
      return sanitized;
    }

    return data;
  }

  /**
   * 清理错误消息
   */
  private sanitizeErrorMessage(message: string): string {
    // 移除可能包含敏感信息的路径
    return message
      .replace(/\/[^\s]+/g, '[PATH_REDACTED]')
      .replace(/\\[^\s]+/g, '[PATH_REDACTED]')
      .replace(/file:\/\/[^\s]+/g, '[FILE_URL_REDACTED]');
  }

  /**
   * 刷新单个事件到日志系统
   */
  private flushEvent(event: AuditEvent): void {
    try {
      const logMessage = this.formatLogMessage(event);

      switch (event.level) {
        case AuditLevel.CRITICAL:
        case AuditLevel.ERROR:
          ProductionLogger.error(logMessage);
          break;
        case AuditLevel.WARN:
          ProductionLogger.warn(logMessage);
          break;
        case AuditLevel.INFO:
        default:
          ProductionLogger.log(logMessage);
          break;
      }

      // 如果启用了远程日志记录，发送到远程系统
      if (this.config.remoteLogging) {
        this.sendToRemoteLogger(event);
      }
    } catch (error) {
      console.error('Failed to flush audit event:', error);
    }
  }

  /**
   * 格式化日志消息
   */
  private formatLogMessage(event: AuditEvent): string {
    const parts = [
      `[AUDIT:${event.level.toUpperCase()}]`,
      `ID:${event.id}`,
      `Category:${event.category}`,
      `Action:${event.action}`,
      `Risk:${event.risk}`,
    ];

    if (event.userId) {
      parts.push(`User:${event.userId}`);
    }

    if (event.resource) {
      parts.push(`Resource:${event.resource}`);
    }

    return parts.join(' ');
  }

  /**
   * 发送到远程日志系统（占位符实现）
   */
  private sendToRemoteLogger(event: AuditEvent): void {
    // 这里可以实现发送到远程日志系统的逻辑
    // 例如：发送到 ELK Stack、Splunk、CloudWatch 等
    ProductionLogger.debug('Remote logging not implemented', event.id);
  }
}

// 导出全局审计日志器实例
export const auditLogger = AuditLogger.getInstance();

// 便捷函数
export function logSecurityEvent(event: SecurityEvent): void {
  auditLogger.logSecurityEvent(event);
}

export function logAccess(
  action: string,
  resource: string,
  userId?: string,
  success?: boolean
): void {
  auditLogger.logAccess(action, resource, userId, success);
}

export function logError(error: Error, context?: Record<string, unknown>): void {
  auditLogger.logError(error, context);
}
