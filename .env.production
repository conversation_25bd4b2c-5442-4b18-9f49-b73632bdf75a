# MCP Prompt Server - 生产环境配置

# 环境设置
NODE_ENV=production

# 安全配置
SECURITY_MAX_STRING_LENGTH=10000
SECURITY_MAX_ARRAY_LENGTH=1000
SECURITY_MAX_PATH_LENGTH=1000
SECURITY_MAX_PERMISSION_STRING_LENGTH=5000
SECURITY_ENABLE_PATTERN_CACHE=true
SECURITY_BLOCK_SUSPICIOUS_INPUT=true
SECURITY_ENABLE_RATE_LIMITING=true
SECURITY_RATE_LIMIT_WINDOW=60000
SECURITY_RATE_LIMIT_MAX_REQUESTS=100

# 性能配置
PERFORMANCE_ENABLE_CACHING=true
PERFORMANCE_CACHE_TTL=300000
PERFORMANCE_MAX_CACHE_SIZE=1000
PERFORMANCE_ENABLE_COMPRESSION=true

# 日志配置
MCP_LOG_LEVEL=info
MCP_LOG_CONSOLE=false
MCP_LOG_FILE=./logs/mcp-server.log

# 提示配置
MCP_PROMPT_DIRS=./dist/prompts,./dist/custom-prompts
MCP_PROMPT_WATCH=false
MCP_PROMPT_CACHE=true
MCP_PROMPT_MAX_SIZE=1048576

# 服务器配置
MCP_SERVER_NAME=mcp-prompt-server
MCP_SERVER_DESCRIPTION=Production MCP Prompt Server with Enterprise Features

# 插件配置
MCP_PLUGINS_ENABLED=validation,cache,metrics,health-check
MCP_MAX_CONCURRENT_TOOLS=50
