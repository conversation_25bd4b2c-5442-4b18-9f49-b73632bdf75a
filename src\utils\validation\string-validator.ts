/**
 * 字符串验证器模块
 * 专门处理字符串相关的验证和清理
 * 企业级安全实现，带缓存和性能优化
 */

import type { ValidationOptions, SecurityEvent } from '../security/types.js';
import { getSecurityConfig } from '../security/config.js';
import { SECURITY_PATTERNS } from '../security/patterns.js';
import { GlobalCacheManager, patternCache } from '../security/cache-manager.js';
import { globalPerformanceMonitor } from '../security/performance-monitor.js';

// 创建专门的缓存实例
const stringValidationCache = GlobalCacheManager.getCache<string>('string-validation');
const booleanValidationCache = GlobalCacheManager.getCache<boolean>('boolean-validation');

/**
 * 字符串验证器类
 * 提供企业级字符串验证和清理功能，带缓存优化
 */
export class StringValidator {
  private static securityEventHandlers: Array<(event: SecurityEvent) => void> = [];

  /**
   * 添加安全事件处理器
   */
  static addSecurityEventHandler(handler: (event: SecurityEvent) => void): void {
    this.securityEventHandlers.push(handler);
  }

  /**
   * 触发安全事件
   */
  private static emitSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): void {
    const fullEvent: SecurityEvent = {
      ...event,
      timestamp: new Date(),
    };

    this.securityEventHandlers.forEach(handler => {
      try {
        handler(fullEvent);
      } catch (error) {
        console.error('Security event handler failed:', error);
      }
    });
  }

  /**
   * 验证并清理字符串输入（带缓存优化）
   */
  static validateString(input: unknown, options: ValidationOptions = {}): string {
    const tracker = globalPerformanceMonitor.startMonitoring('StringValidator.validateString');

    try {
      if (typeof input !== 'string') {
        throw new Error('Input must be a string');
      }

      const config = getSecurityConfig();
      const finalOptions = {
        maxLength: config.maxStringLength,
        sanitize: true,
        ...options,
      };

      // 检查缓存
      const cacheKey = `validate_${input.length}_${finalOptions.maxLength}_${input.substring(0, 50)}`;
      const cached = stringValidationCache.get(cacheKey);
      if (cached !== undefined && typeof cached === 'string') {
        return cached;
      }

      let value = input;

      // 清理输入
      if (finalOptions.sanitize !== false) {
        value = this.sanitizeString(value);
      }

      // 长度验证
      if (finalOptions.minLength !== undefined && value.length < finalOptions.minLength) {
        throw new Error(`Input must be at least ${finalOptions.minLength} characters long`);
      }

      if (finalOptions.maxLength !== undefined && value.length > finalOptions.maxLength) {
        this.emitSecurityEvent({
          type: 'security_violation',
          message: `String too long: ${value.length} > ${finalOptions.maxLength}`,
          input: `${value.substring(0, 100)}...`,
          severity: 'medium',
        });
        throw new Error(`String too long`);
      }

      // 模式验证
      if (finalOptions.pattern && !finalOptions.pattern.test(value)) {
        this.emitSecurityEvent({
          type: 'validation_failed',
          message: 'Input does not match required pattern',
          input: value.substring(0, 100),
          severity: 'low',
        });
        throw new Error('Input does not match required pattern');
      }

      // 缓存结果
      stringValidationCache.set(cacheKey, value);

      return value;
    } finally {
      tracker.end();
    }
  }

  /**
   * 企业级字符串清理，防御多种攻击向量（增强版本）
   * 提供全面的安全防护，防止XSS、注入攻击、编码绕过等
   */
  static sanitizeString(input: string): string {
    const tracker = globalPerformanceMonitor.startMonitoring('StringValidator.sanitizeString');

    try {
      // 检查缓存
      const cacheKey = `sanitize_${input.length}_${input.substring(0, 100)}`;
      const cached = stringValidationCache.get(cacheKey);
      if (cached !== undefined && typeof cached === 'string') {
        return cached;
      }

      let cleaned = input;

      // 第一阶段：移除控制字符和不可见字符（先处理，避免绕过）
      cleaned = cleaned
        .replace(SECURITY_PATTERNS.CONTROL_CHARS, '')
        .replace(SECURITY_PATTERNS.UNICODE_BYPASS, '')
        .replace(SECURITY_PATTERNS.HOMOGRAPH_ATTACK, '')
        .replace(SECURITY_PATTERNS.NULL_BYTE, ''); // 移除空字节

      // 第二阶段：处理编码绕过攻击（在HTML实体解码之前）
      cleaned = cleaned
        .replace(SECURITY_PATTERNS.DOUBLE_ENCODING, '') // 移除双重编码
        .replace(/%[0-9a-f]{2}/gi, '') // 移除URL编码
        .replace(/\\x[0-9a-f]{2}/gi, '') // 移除十六进制编码
        .replace(/\\u[0-9a-f]{4}/gi, '') // 移除Unicode编码
        .replace(/\\[0-7]{1,3}/g, ''); // 移除八进制编码

      // 第三阶段：HTML实体解码（用于检测编码绕过）
      cleaned = cleaned
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#x27;/g, "'")
        .replace(/&#x2F;/g, '/')
        .replace(/&amp;/g, '&'); // 最后处理&符号

      // 第四阶段：移除危险的HTML和脚本内容
      cleaned = cleaned
        .replace(SECURITY_PATTERNS.SCRIPT_TAGS, '')
        .replace(SECURITY_PATTERNS.STYLE_TAGS, '')
        .replace(SECURITY_PATTERNS.EVENT_HANDLERS, '')
        .replace(SECURITY_PATTERNS.JAVASCRIPT_PROTOCOL, '')
        .replace(SECURITY_PATTERNS.VBSCRIPT_PROTOCOL, '')
        .replace(SECURITY_PATTERNS.DATA_PROTOCOL, '');

      // 第五阶段：防止各种注入攻击
      cleaned = cleaned
        .replace(SECURITY_PATTERNS.SQL_INJECTION, '')
        // 移除危险的命令注入字符，但保留表达式中的逻辑运算符
        .replace(/[;`${}[\]\\]/g, '') // 移除明确的命令注入字符
        .replace(/(?<![&|])[&|](?![&|])/g, '') // 移除单独的&和|，但保留&&和||
        .replace(/<!--[\s\S]*?-->/g, '') // 移除HTML注释
        .replace(/<!\[CDATA\[[\s\S]*?\]\]>/g, '') // 移除CDATA块
        .replace(/&\w+;/g, match => {
          // 只保留安全的HTML实体
          const safeEntities = ['&amp;', '&lt;', '&gt;', '&quot;', '&#39;'];
          return safeEntities.includes(match) ? match : '';
        });

      // 第六阶段：增强的同形异义字攻击防护
      cleaned = cleaned
        // 西里尔字母
        .replace(/[\u0430-\u044F\u0410-\u042F]/g, '')
        // 希腊字母
        .replace(/[\u0370-\u03FF]/g, '')
        // 阿拉伯字母
        .replace(/[\u0600-\u06FF]/g, '')
        // 中文字符（如果不需要支持）
        .replace(/[\u4E00-\u9FFF]/g, '')
        // 其他可能的混淆字符
        .replace(/[\u2000-\u206F]/g, ''); // 通用标点符号

      // 第七阶段：标准化和最终清理
      cleaned = cleaned
        .replace(/\s+/g, ' ') // 标准化空白字符
        .replace(/[^\x20-\x7E\u00A0-\u00FF]/g, '') // 只保留基本拉丁字符和拉丁补充字符
        .trim();

      // 缓存结果
      stringValidationCache.set(cacheKey, cleaned);

      return cleaned;
    } finally {
      tracker.end();
    }
  }

  /**
   * 验证字符串是否为有效的标识符
   */
  static validateIdentifier(input: string): boolean {
    const tracker = globalPerformanceMonitor.startMonitoring('StringValidator.validateIdentifier');

    try {
      // 检查缓存
      const cacheKey = `identifier_${input}`;
      const cached = booleanValidationCache.get(cacheKey);
      if (cached !== undefined && typeof cached === 'boolean') {
        return cached;
      }

      const isValid = /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(input) && input.length <= 100;

      // 缓存结果
      booleanValidationCache.set(cacheKey, isValid);

      return isValid;
    } finally {
      tracker.end();
    }
  }

  /**
   * 验证字符串是否包含危险模式
   */
  static containsDangerousPatterns(input: string): boolean {
    const tracker = globalPerformanceMonitor.startMonitoring(
      'StringValidator.containsDangerousPatterns'
    );

    try {
      // 检查缓存
      const cacheKey = `dangerous_${input.substring(0, 100)}`;
      const cached = booleanValidationCache.get(cacheKey);
      if (cached !== undefined && typeof cached === 'boolean') {
        return cached;
      }

      const dangerousPatterns = [
        SECURITY_PATTERNS.SCRIPT_TAGS,
        SECURITY_PATTERNS.EVENT_HANDLERS,
        SECURITY_PATTERNS.JAVASCRIPT_PROTOCOL,
        SECURITY_PATTERNS.SQL_INJECTION,
        SECURITY_PATTERNS.COMMAND_INJECTION,
        SECURITY_PATTERNS.PATH_TRAVERSAL,
      ];

      const hasDangerous = dangerousPatterns.some(pattern => {
        // 使用缓存的正则表达式
        const cachedPattern = patternCache.get(pattern.source);
        const regex = cachedPattern || pattern;
        if (!cachedPattern) {
          patternCache.set(pattern.source, pattern);
        }
        return regex.test(input);
      });

      // 缓存结果
      booleanValidationCache.set(cacheKey, hasDangerous);

      return hasDangerous;
    } finally {
      tracker.end();
    }
  }

  /**
   * 获取验证统计信息
   */
  static getValidationStats(): Record<string, unknown> {
    return {
      stringValidationCache: stringValidationCache.getStats(),
      booleanValidationCache: booleanValidationCache.getStats(),
      patternCache: patternCache.getStats(),
      performance: globalPerformanceMonitor.getStats(),
    };
  }

  /**
   * 验证布尔值输入
   */
  static validateBoolean(input: unknown): boolean {
    if (typeof input === 'boolean') {
      return input;
    }

    if (typeof input === 'string') {
      const normalized = input.toLowerCase().trim();
      if (['true', '1', 'yes', 'on'].includes(normalized)) {
        return true;
      }
      if (['false', '0', 'no', 'off'].includes(normalized)) {
        return false;
      }
    }

    throw new Error('Invalid boolean value');
  }

  /**
   * 验证枚举值
   */
  static validateEnum<T extends string | number>(input: unknown, allowedValues: readonly T[]): T {
    if (typeof input === 'string' || typeof input === 'number') {
      const value = input as T;
      if (allowedValues.includes(value)) {
        return value;
      }
    }

    throw new Error(`Value must be one of: ${allowedValues.join(', ')}`);
  }

  /**
   * 验证JSON字符串
   */
  static validateJson<T = unknown>(input: unknown): T {
    if (typeof input !== 'string') {
      throw new Error('Input must be a JSON string');
    }

    try {
      return JSON.parse(input) as T;
    } catch {
      throw new Error('Invalid JSON format');
    }
  }

  /**
   * 验证Base64字符串
   */
  static validateBase64(input: unknown): string {
    const str = this.validateString(input);
    const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;

    if (!base64Pattern.test(str)) {
      throw new Error('Invalid Base64 format');
    }

    return str;
  }

  /**
   * 验证十六进制字符串
   */
  static validateHex(input: unknown): string {
    const str = this.validateString(input);
    const hexPattern = /^[0-9a-fA-F]+$/;

    if (!hexPattern.test(str)) {
      throw new Error('Invalid hexadecimal format');
    }

    return str;
  }

  /**
   * 验证UUID
   */
  static validateUuid(input: unknown, version?: number): string {
    const str = this.validateString(input);
    const uuidPattern =
      version === 4
        ? /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
        : /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

    if (!uuidPattern.test(str)) {
      throw new Error(`Invalid UUID${version ? ` v${version}` : ''} format`);
    }

    return str;
  }

  /**
   * 验证颜色值
   */
  static validateColor(input: unknown): string {
    const str = this.validateString(input);
    const colorPattern = /^#([0-9a-fA-F]{3}|[0-9a-fA-F]{6})$/;

    if (!colorPattern.test(str)) {
      throw new Error('Invalid color format (expected #RGB or #RRGGBB)');
    }

    return str;
  }

  /**
   * 验证时区
   */
  static validateTimezone(input: unknown): string {
    const str = this.validateString(input);
    // 简单的时区验证
    const timezonePattern = /^[A-Za-z_]+\/[A-Za-z_]+$/;

    if (!timezonePattern.test(str)) {
      throw new Error('Invalid timezone format');
    }

    return str;
  }

  /**
   * 验证语言代码
   */
  static validateLanguageCode(input: unknown): string {
    const str = this.validateString(input);
    const langPattern = /^[a-z]{2}(-[A-Z]{2})?$/;

    if (!langPattern.test(str)) {
      throw new Error('Invalid language code format (expected: en, en-US)');
    }

    return str;
  }

  /**
   * 验证版本号
   */
  static validateVersion(input: unknown): string {
    const str = this.validateString(input);
    const versionPattern = /^\d+\.\d+\.\d+(-[a-zA-Z0-9]+)?$/;

    if (!versionPattern.test(str)) {
      throw new Error('Invalid version format (expected: x.y.z or x.y.z-suffix)');
    }

    return str;
  }

  /**
   * 验证哈希值
   */
  static validateHash(input: unknown, algorithm: 'md5' | 'sha1' | 'sha256' | 'sha512'): string {
    const str = this.validateString(input);
    const patterns = {
      md5: /^[a-f0-9]{32}$/i,
      sha1: /^[a-f0-9]{40}$/i,
      sha256: /^[a-f0-9]{64}$/i,
      sha512: /^[a-f0-9]{128}$/i,
    };

    const pattern = patterns[algorithm];
    if (!pattern.test(str)) {
      throw new Error(`Invalid ${algorithm.toUpperCase()} hash format`);
    }

    return str;
  }

  /**
   * 清理缓存
   */
  static clearCache(): void {
    stringValidationCache.clear();
    booleanValidationCache.clear();
    patternCache.clear();
  }
}
