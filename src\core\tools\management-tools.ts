/**
 * 管理工具注册器 - 负责注册服务器管理相关的MCP工具
 * 包含：reload_prompts, get_prompt_names, get_server_status, prompts_list, prompts_get
 */

import type { ILogger } from '../../services/logger.service.js';
import type { IPromptLoader } from '../prompt-loader.js';
import type { IToolRegistry } from '../tool-registry.js';
import type { IMetricsService } from '../../services/metrics.service.js';
import type { PromptTemplate } from '../../types/prompt.js';
import type { ServerConfig } from '../../types/config.js';
import type {
  IUnifiedToolRegistry,
  ToolDefinition,
  ToolHandler,
} from '../unified-tool-registry.js';

export interface IManagementToolsRegistrar {
  registerManagementTools(): void;
}

export class ManagementToolsRegistrar implements IManagementToolsRegistrar {
  private readonly REGISTRAR_NAME = 'ManagementTools';

  constructor(
    private unifiedRegistry: IUnifiedToolRegistry,
    private logger: ILogger,
    private promptLoader: IPromptLoader,
    private toolRegistry: IToolRegistry,
    private metrics: IMetricsService,
    private config: ServerConfig,
    private getStartTime: () => number,
    private getIsRunning: () => boolean,
    private emitEvent: (event: string, ...args: unknown[]) => void,
    private reloadPrompts: () => Promise<void>
  ) {}

  /**
   * 注册所有管理工具
   */
  registerManagementTools(): void {
    // 注册所有管理工具到统一注册表
    this.registerAllManagementTools();

    this.logger.info('Management tools registered successfully');
  }

  /**
   * 注册所有管理工具到统一注册表
   */
  private registerAllManagementTools(): void {
    const tools = this.getManagementToolDefinitions();

    for (const tool of tools) {
      this.unifiedRegistry.registerTool(tool, this.createToolHandler(), this.REGISTRAR_NAME);
    }
  }

  /**
   * 创建工具处理器
   */
  private createToolHandler(): ToolHandler {
    return async (name: string, args: Record<string, unknown>) => {
      return this.handleToolCall(name, args);
    };
  }

  /**
   * 获取所有管理工具定义
   */
  private getManagementToolDefinitions(): ToolDefinition[] {
    return [
      {
        name: 'reload_prompts',
        description: '重新加载所有预设的prompts',
        inputSchema: {
          type: 'object',
          properties: {},
          additionalProperties: false,
        },
      },
      {
        name: 'get_prompt_names',
        description: '获取所有可用的prompt名称',
        inputSchema: {
          type: 'object',
          properties: {},
          additionalProperties: false,
        },
      },
      {
        name: 'get_server_status',
        description: '获取服务器运行状态和统计信息',
        inputSchema: {
          type: 'object',
          properties: {},
          additionalProperties: false,
        },
      },
      {
        name: 'prompts_list',
        description: '获取所有可用的提示模板',
        inputSchema: {
          type: 'object',
          properties: {},
          additionalProperties: false,
        },
      },
      {
        name: 'prompts_get',
        description: '获取指定的提示模板内容',
        inputSchema: {
          type: 'object',
          properties: {
            name: { type: 'string', description: '提示模板名称' },
            arguments: { type: 'object', description: '提示参数' },
          },
          required: ['name'],
          additionalProperties: false,
        },
      },
    ];
  }

  /**
   * 处理工具调用
   */
  private async handleToolCall(
    name: string,
    args: Record<string, unknown>
  ): Promise<{
    content: Array<{
      type: 'text';
      text: string;
    }>;
    isError?: boolean;
  }> {
    switch (name) {
      case 'reload_prompts':
        return this.handleReloadPrompts();

      case 'get_prompt_names':
        return this.handleGetPromptNames();

      case 'get_server_status':
        return this.handleGetServerStatus();

      case 'prompts_list':
        return this.handlePromptsList();

      case 'prompts_get':
        return this.handlePromptsGet(args);

      default:
        throw new Error(`Unknown tool: ${name}`);
    }
  }

  /**
   * 处理重新加载提示工具调用
   */
  private async handleReloadPrompts(): Promise<{
    content: Array<{
      type: 'text';
      text: string;
    }>;
  }> {
    try {
      // 记录重新加载前的状态
      const beforePrompts = this.promptLoader.getLoadedPrompts();
      this.logger.debug(`ManagementTools - Before reload:`, {
        promptsCount: beforePrompts.length,
        serverPid: process.pid,
        serverStartTime: new Date(this.getStartTime()).toISOString()
      });

      await this.reloadPrompts();
      this.emitEvent('prompts:reloaded');

      // 记录重新加载后的状态
      const afterPrompts = this.promptLoader.getLoadedPrompts();
      this.logger.debug(`ManagementTools - After reload:`, {
        promptsCount: afterPrompts.length,
        serverPid: process.pid,
        serverStartTime: new Date(this.getStartTime()).toISOString()
      });

      const result = {
        message: 'Prompts reloaded successfully',
        beforeCount: beforePrompts.length,
        afterCount: afterPrompts.length,
        serverInfo: {
          pid: process.pid,
          startTime: new Date(this.getStartTime()).toISOString(),
          uptime: Date.now() - this.getStartTime()
        },
        timestamp: new Date().toISOString()
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      this.logger.error('Failed to reload prompts', error as Error);
      throw new Error(
        `Failed to reload prompts: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * 处理获取提示名称工具调用
   */
  private async handleGetPromptNames(): Promise<{
    content: Array<{
      type: 'text';
      text: string;
    }>;
  }> {
    try {
      const prompts = this.promptLoader.getLoadedPrompts();
      const promptNames = prompts.map((p: PromptTemplate) => p.name);

      const result = {
        promptNames,
        totalCount: promptNames.length,
        categories: this.categorizePrompts(prompts),
        lastUpdated: new Date().toISOString(),
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      this.logger.error('Failed to get prompt names', error as Error);
      throw new Error(
        `Failed to get prompt names: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * 处理获取服务器状态工具调用
   */
  private async handleGetServerStatus(): Promise<{
    content: Array<{
      type: 'text';
      text: string;
    }>;
  }> {
    try {
      const startTime = this.getStartTime();
      const uptime = Date.now() - startTime;
      const prompts = this.promptLoader.getLoadedPrompts();
      const registeredTools = this.toolRegistry.getRegisteredTools();

      // 添加调试日志
      this.logger.debug(`ManagementTools - Server status check:`, {
        startTime,
        uptime,
        promptsCount: prompts.length,
        toolsCount: registeredTools.length,
        isRunning: this.getIsRunning(),
        promptLoaderType: this.promptLoader.constructor.name,
        serverPid: process.pid,
        serverStartTime: new Date(startTime).toISOString(),
        promptNames: prompts.map((p: PromptTemplate) => p.name).slice(0, 5) // 显示前5个prompt名称
      });

      const status = {
        server: {
          name: this.config.server.name,
          version: this.config.server.version,
          uptime: Math.floor(uptime / 1000),
          isRunning: this.getIsRunning(),
          startTime: new Date(startTime).toISOString(),
        },
        prompts: {
          total: prompts.length,
          loaded: prompts.map((p: PromptTemplate) => p.name),
        },
        tools: {
          total: registeredTools.length,
          registered: registeredTools,
        },
        metrics: this.metrics.getMetrics(),
        timestamp: new Date().toISOString(),
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(status, null, 2),
          },
        ],
      };
    } catch (error) {
      this.logger.error('Failed to get server status', error as Error);
      throw new Error(
        `Failed to get server status: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * 处理获取提示列表工具调用
   */
  private async handlePromptsList(): Promise<{
    content: Array<{
      type: 'text';
      text: string;
    }>;
  }> {
    try {
      const prompts = this.promptLoader.getLoadedPrompts();

      // 添加详细的调试信息
      this.logger.debug(`ManagementTools - Prompts list request:`, {
        promptsCount: prompts.length,
        serverPid: process.pid,
        serverStartTime: new Date(this.getStartTime()).toISOString(),
        promptLoaderType: this.promptLoader.constructor.name,
        firstFewPrompts: prompts.slice(0, 3).map((p: PromptTemplate) => p.name)
      });

      const promptList = prompts.map((prompt: PromptTemplate) => ({
        name: prompt.name,
        description: prompt.description || '',
        arguments: prompt.arguments || [],
        category: prompt.name.split('_')[0] || 'other',
      }));

      const result = {
        prompts: promptList,
        totalCount: promptList.length,
        categories: this.categorizePrompts(prompts),
        timestamp: new Date().toISOString(),
        serverInfo: {
          pid: process.pid,
          startTime: new Date(this.getStartTime()).toISOString(),
          uptime: Date.now() - this.getStartTime()
        }
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      this.logger.error('Failed to list prompts', error as Error);
      throw new Error(
        `Failed to list prompts: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * 处理获取指定提示工具调用
   */
  private async handlePromptsGet(args: Record<string, unknown>): Promise<{
    content: Array<{
      type: 'text';
      text: string;
    }>;
  }> {
    const { name, arguments: promptArgs } = args as {
      name: string;
      arguments?: Record<string, unknown>;
    };

    try {
      const prompts = this.promptLoader.getLoadedPrompts();
      const prompt = prompts.find((p: PromptTemplate) => p.name === name);

      if (!prompt) {
        throw new Error(`Prompt not found: ${name}`);
      }

      // 处理提示内容 - 简单返回提示信息
      const processedContent = JSON.stringify(
        {
          name: prompt.name,
          description: prompt.description,
          arguments: prompt.arguments,
          messages: prompt.messages,
          providedArgs: promptArgs,
        },
        null,
        2
      );

      return {
        content: [
          {
            type: 'text',
            text: processedContent,
          },
        ],
      };
    } catch (error) {
      this.logger.error('Failed to get prompt', error as Error);
      throw new Error(
        `Failed to get prompt: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * 将提示按类别分组
   */
  private categorizePrompts(prompts: PromptTemplate[]): Record<string, string[]> {
    const categories: Record<string, string[]> = {};

    prompts.forEach((prompt: PromptTemplate) => {
      const category = prompt.name.split('_')[0] || 'other';
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(prompt.name);
    });

    return categories;
  }
}
