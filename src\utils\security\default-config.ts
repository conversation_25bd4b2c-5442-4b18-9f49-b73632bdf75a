/**
 * 默认安全配置定义
 * 适合大多数生产环境的安全设置
 */

import type { SecurityConfig, SecurityPolicy, CacheConfig, PerformanceConfig } from './types.js';
import { EnvironmentParser } from './environment-parser.js';

/**
 * 默认安全配置
 * 适合大多数生产环境的安全设置
 */
export const DEFAULT_SECURITY_CONFIG: SecurityConfig = {
  // 字符串限制（使用安全解析器）
  maxStringLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_STRING_LENGTH'],
    10000,
    1,
    1000000
  ),
  maxTemplateLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_TEMPLATE_LENGTH'],
    50000,
    1000,
    10000000
  ),
  maxExpressionLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_EXPRESSION_LENGTH'],
    1000,
    10,
    10000
  ),

  // 数组限制
  maxArrayLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_ARRAY_LENGTH'],
    1000,
    1,
    100000
  ),
  maxLoopIterations: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_LOOP_ITERATIONS'],
    100,
    1,
    10000
  ),

  // 文件路径限制
  maxPathLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_PATH_LENGTH'],
    1000,
    10,
    10000
  ),
  maxPathDepth: EnvironmentParser.parseInt(process.env['SECURITY_MAX_PATH_DEPTH'], 10, 1, 100),
  maxPathComponentLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_PATH_COMPONENT_LENGTH'],
    255,
    1,
    1000
  ),

  // 权限限制
  maxPermissionsLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_PERMISSIONS_LENGTH'],
    5000,
    100,
    50000
  ),
  maxPermissionComponents: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_PERMISSION_COMPONENTS'],
    100,
    1,
    1000
  ),
  maxPermissionComponentLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_PERMISSION_COMPONENT_LENGTH'],
    100,
    1,
    1000
  ),

  // 表达式复杂度限制
  maxOperatorCount: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_OPERATOR_COUNT'],
    100,
    1,
    1000
  ),
  maxExpressionDepth: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_EXPRESSION_DEPTH'],
    10,
    1,
    100
  ),

  // URL限制
  maxUrlLength: EnvironmentParser.parseInt(process.env['SECURITY_MAX_URL_LENGTH'], 2048, 10, 10000),

  // 邮箱限制
  maxEmailLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_EMAIL_LENGTH'],
    254,
    5,
    1000
  ),
  maxEmailLocalPartLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_EMAIL_LOCAL_PART_LENGTH'],
    64,
    1,
    100
  ),
  maxEmailDomainPartLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_EMAIL_DOMAIN_PART_LENGTH'],
    253,
    4,
    500
  ),

  // 性能限制
  maxProcessingTime: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_PROCESSING_TIME'],
    5000,
    100,
    60000
  ),
  maxMemoryUsage: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_MEMORY_USAGE'],
    100 * 1024 * 1024, // 100MB
    1024 * 1024, // 1MB
    1024 * 1024 * 1024 // 1GB
  ),

  // 缓存配置
  enablePatternCache: EnvironmentParser.parseBoolean(
    process.env['SECURITY_ENABLE_PATTERN_CACHE'],
    true
  ),
  maxCacheSize: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_CACHE_SIZE'],
    1000,
    10,
    100000
  ),
  cacheTimeout: EnvironmentParser.parseInt(
    process.env['SECURITY_CACHE_TIMEOUT'],
    300000, // 5分钟
    1000,
    3600000 // 1小时
  ),
};

/**
 * 默认安全策略
 */
export const DEFAULT_SECURITY_POLICY: SecurityPolicy = {
  strictMode: EnvironmentParser.parseBoolean(process.env['SECURITY_STRICT_MODE'], false),
  allowDangerousOperations: EnvironmentParser.parseBoolean(
    process.env['SECURITY_ALLOW_DANGEROUS_OPERATIONS'],
    false
  ),
  logSecurityEvents: EnvironmentParser.parseBoolean(process.env['SECURITY_LOG_EVENTS'], true),
  blockSuspiciousInput: EnvironmentParser.parseBoolean(
    process.env['SECURITY_BLOCK_SUSPICIOUS_INPUT'],
    true
  ),
  enableRateLimiting: EnvironmentParser.parseBoolean(
    process.env['SECURITY_ENABLE_RATE_LIMITING'],
    true
  ),
  maxRequestsPerMinute: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_REQUESTS_PER_MINUTE'],
    100,
    1,
    10000
  ),
};

/**
 * 默认缓存配置
 */
export const DEFAULT_CACHE_CONFIG: CacheConfig = {
  enabled: EnvironmentParser.parseBoolean(process.env['CACHE_ENABLED'], true),
  maxSize: EnvironmentParser.parseInt(process.env['CACHE_MAX_SIZE'], 1000, 10, 100000),
  ttl: EnvironmentParser.parseInt(
    process.env['CACHE_TTL'],
    300000, // 5分钟
    1000,
    3600000 // 1小时
  ),
  cleanupInterval: EnvironmentParser.parseInt(
    process.env['CACHE_CLEANUP_INTERVAL'],
    60000, // 1分钟
    1000,
    600000 // 10分钟
  ),
};

/**
 * 默认性能配置
 */
export const DEFAULT_PERFORMANCE_CONFIG: PerformanceConfig = {
  enableMonitoring: EnvironmentParser.parseBoolean(
    process.env['PERFORMANCE_ENABLE_MONITORING'],
    true
  ),
  maxExecutionTime: EnvironmentParser.parseInt(
    process.env['PERFORMANCE_MAX_EXECUTION_TIME'],
    5000,
    100,
    60000
  ),
  memoryThreshold: EnvironmentParser.parseInt(
    process.env['PERFORMANCE_MEMORY_THRESHOLD'],
    50 * 1024 * 1024, // 50MB
    1024 * 1024, // 1MB
    500 * 1024 * 1024 // 500MB
  ),
  logSlowOperations: EnvironmentParser.parseBoolean(
    process.env['PERFORMANCE_LOG_SLOW_OPERATIONS'],
    true
  ),
};

/**
 * 验证默认配置的完整性
 */
export function validateDefaultConfigs(): void {
  const config = DEFAULT_SECURITY_CONFIG;

  // 验证基本范围
  if (config.maxStringLength <= 0 || config.maxStringLength > 10000000) {
    throw new Error('Invalid maxStringLength in default config');
  }

  if (config.maxArrayLength <= 0 || config.maxArrayLength > 1000000) {
    throw new Error('Invalid maxArrayLength in default config');
  }

  if (config.maxProcessingTime <= 0 || config.maxProcessingTime > 300000) {
    throw new Error('Invalid maxProcessingTime in default config');
  }

  // 验证路径配置
  if (config.maxPathDepth <= 0 || config.maxPathDepth > 100) {
    throw new Error('Invalid maxPathDepth in default config');
  }

  // 验证性能配置
  const perfConfig = DEFAULT_PERFORMANCE_CONFIG;
  if (perfConfig.maxExecutionTime <= 0 || perfConfig.maxExecutionTime > 300000) {
    throw new Error('Invalid maxExecutionTime in performance config');
  }
}
