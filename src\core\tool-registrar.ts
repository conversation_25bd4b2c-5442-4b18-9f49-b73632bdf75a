/**
 * MCP工具注册器 - 负责注册所有MCP工具
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import type { ILogger } from '../services/logger.service.js';
import type { IMCPClientConfigService } from '../services/mcp-client-config.service.js';
import type { IMCPClientManager } from '../services/mcp-client-manager.service.js';
import type { IToolRegistry } from './tool-registry.js';
import type { IMetricsService } from '../services/metrics.service.js';
import type { IPromptLoader } from './prompt-loader.js';
import type { ServerConfig } from '../types/config.js';
import { UnifiedToolRegistry, type IUnifiedToolRegistry } from './unified-tool-registry.js';
import {
  ManagementToolsRegistrar,
  type IManagementToolsRegistrar,
} from './tools/management-tools.js';
import {
  MCPClientToolsRegistrar,
  type IMCPClientToolsRegistrar,
} from './tools/mcp-client-tools.js';
import { MarkdownToolsRegistrar, type IMarkdownToolsRegistrar } from './tools/markdown-tools.js';
import { CodeReviewToolsRegistrar, type ICodeReviewToolsRegistrar } from './tools/code-review-tools.js';

export interface IToolRegistrar {
  registerAllTools(): void;
  getUnifiedRegistry(): IUnifiedToolRegistry;
}

export class ToolRegistrar implements IToolRegistrar {
  private unifiedRegistry: IUnifiedToolRegistry;
  private managementTools: IManagementToolsRegistrar;
  private mcpClientTools: IMCPClientToolsRegistrar;
  private markdownTools: IMarkdownToolsRegistrar;
  private codeReviewTools: ICodeReviewToolsRegistrar;

  constructor(
    private mcpServer: Server,
    private logger: ILogger,
    private toolRegistry: IToolRegistry,
    private metrics: IMetricsService,
    private mcpClientConfigService: IMCPClientConfigService,
    private mcpClientManager: IMCPClientManager,
    private promptLoader: IPromptLoader,
    private getStartTime: () => number,
    private config: ServerConfig,
    private getIsRunning: () => boolean,
    private emitEvent: (event: string, ...args: unknown[]) => void,
    private reloadPrompts: () => Promise<void>
  ) {
    // 初始化统一工具注册表管理器
    this.unifiedRegistry = new UnifiedToolRegistry(this.mcpServer, this.logger);

    // 初始化专门的工具注册器
    this.managementTools = new ManagementToolsRegistrar(
      this.unifiedRegistry,
      this.logger,
      this.promptLoader,
      this.toolRegistry,
      this.metrics,
      this.config,
      this.getStartTime,
      this.getIsRunning,
      this.emitEvent,
      this.reloadPrompts
    );

    this.mcpClientTools = new MCPClientToolsRegistrar(
      this.unifiedRegistry,
      this.logger,
      this.mcpClientConfigService,
      this.mcpClientManager
    );

    this.markdownTools = new MarkdownToolsRegistrar(this.unifiedRegistry, this.logger);

    this.codeReviewTools = new CodeReviewToolsRegistrar(this.unifiedRegistry, this.logger);
  }

  /**
   * 注册所有MCP工具
   */
  registerAllTools(): void {
    try {
      // 首先初始化统一工具注册表管理器
      this.unifiedRegistry.initialize();
      this.logger.debug('UnifiedToolRegistry initialized');

      // 使用专门的工具注册器注册工具到统一注册表
      this.logger.debug('Starting tool registration process');

      // 注册管理工具
      this.managementTools.registerManagementTools();
      const managementToolCount = this.unifiedRegistry.getRegistrarTools('ManagementTools').length;
      this.logger.debug(`Registered ${managementToolCount} management tools`);

      // 注册MCP客户端工具
      this.mcpClientTools.registerMCPClientTools();
      const mcpClientToolCount = this.unifiedRegistry.getRegistrarTools('MCPClientTools').length;
      this.logger.debug(`Registered ${mcpClientToolCount} MCP client tools`);

      // 注册Markdown工具
      this.markdownTools.registerMarkdownTools();
      const markdownToolCount = this.unifiedRegistry.getRegistrarTools('MarkdownTools').length;
      this.logger.debug(`Registered ${markdownToolCount} Markdown tools`);

      // 注册代码审查工具
      this.codeReviewTools.registerCodeReviewTools();
      const codeReviewToolCount = this.unifiedRegistry.getRegistrarTools('CodeReviewTools').length;
      this.logger.debug(`Registered ${codeReviewToolCount} Code Review tools`);

      // 获取总工具数量并记录成功信息
      const totalToolCount = this.unifiedRegistry.getToolCount();
      const allTools = this.unifiedRegistry.getRegisteredTools();

      this.logger.info(`All MCP tools registered successfully. Total tools: ${totalToolCount}`);
      this.logger.debug(`Registered tools: ${allTools.map(tool => tool.name).join(', ')}`);

      // 验证所有工具都已正确注册
      this.validateToolRegistration();
    } catch (error) {
      this.logger.error('Failed to register MCP tools', error as Error);
      throw new Error(
        `Tool registration failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * 验证工具注册是否成功
   */
  private validateToolRegistration(): void {
    const totalTools = this.unifiedRegistry.getToolCount();

    if (totalTools === 0) {
      throw new Error('No tools were registered - this indicates a critical registration failure');
    }

    // 验证每个注册器都至少注册了一些工具
    const managementTools = this.unifiedRegistry.getRegistrarTools('ManagementTools');
    const mcpClientTools = this.unifiedRegistry.getRegistrarTools('MCPClientTools');
    const markdownTools = this.unifiedRegistry.getRegistrarTools('MarkdownTools');

    if (managementTools.length === 0) {
      this.logger.warn('No management tools were registered');
    }

    if (mcpClientTools.length === 0) {
      this.logger.warn('No MCP client tools were registered');
    }

    if (markdownTools.length === 0) {
      this.logger.warn('No Markdown tools were registered');
    }

    this.logger.debug(
      `Tool registration validation completed. Management: ${managementTools.length}, MCP Client: ${mcpClientTools.length}, Markdown: ${markdownTools.length}`
    );
  }

  /**
   * 获取统一工具注册表实例（用于测试和调试）
   */
  public getUnifiedRegistry(): IUnifiedToolRegistry {
    return this.unifiedRegistry;
  }

  /**
   * 获取所有已注册工具的详细信息
   */
  public getRegisteredToolsInfo(): {
    totalCount: number;
    toolsByRegistrar: Record<string, string[]>;
    allToolNames: string[];
  } {
    const allTools = this.unifiedRegistry.getRegisteredTools();
    const managementTools = this.unifiedRegistry.getRegistrarTools('ManagementTools');
    const mcpClientTools = this.unifiedRegistry.getRegistrarTools('MCPClientTools');
    const markdownTools = this.unifiedRegistry.getRegistrarTools('MarkdownTools');

    return {
      totalCount: this.unifiedRegistry.getToolCount(),
      toolsByRegistrar: {
        ManagementTools: managementTools.map(tool => tool.name),
        MCPClientTools: mcpClientTools.map(tool => tool.name),
        MarkdownTools: markdownTools.map(tool => tool.name),
      },
      allToolNames: allTools.map(tool => tool.name),
    };
  }
}
