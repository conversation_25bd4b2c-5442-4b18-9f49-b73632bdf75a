/**
 * MCP客户端配置解析服务
 * 用于解析、验证和管理MCP客户端配置文件
 */

import * as fs from 'fs-extra';
import path from 'path';
import { z } from 'zod';
import type { ILogger } from './logger.service.js';
import type {
  MCPClientConfig,
  MCPServerConfig,
  ConfigParseResult,
  ConfigParseError,
  ConfigParseWarning,
  MCPServerStatus,
} from '../types/mcp-client.js';

// Zod验证Schema
const MCPServerConfigSchema = z.object({
  command: z.string().min(1, 'Command cannot be empty'),
  args: z.array(z.string()),
  env: z.record(z.string()).optional(),
  description: z.string().optional(),
  cwd: z.string().optional(),
  transport: z.enum(['stdio', 'http', 'sse']).optional(),
  url: z.string().url().optional(),
  timeout: z.number().positive().optional(),
  retries: z.number().nonnegative().optional(),
});

const MCPClientConfigSchema = z.object({
  mcpServers: z.record(MCPServerConfigSchema),
  globalSettings: z
    .object({
      timeout: z.number().positive().optional(),
      retryAttempts: z.number().nonnegative().optional(),
      logLevel: z.enum(['debug', 'info', 'warn', 'error']).optional(),
      maxConcurrentServers: z.number().positive().optional(),
    })
    .optional(),
});

export interface IMCPClientConfigService {
  parseConfig(configPath: string): Promise<ConfigParseResult>;
  parseConfigFromString(configContent: string): ConfigParseResult;
  validateConfig(config: unknown): ConfigParseResult;
  mergeConfigs(
    baseConfig: MCPClientConfig,
    ...configs: Partial<MCPClientConfig>[]
  ): MCPClientConfig;
  exportConfig(config: MCPClientConfig, outputPath: string): Promise<void>;
  getServerStatus(serverName: string, config: MCPServerConfig): Promise<MCPServerStatus>;
  listServers(config: MCPClientConfig): string[];
}

export class MCPClientConfigService implements IMCPClientConfigService {
  private logger: ILogger;

  constructor(logger: ILogger) {
    this.logger = logger.forComponent('MCPClientConfigService');
  }

  /**
   * 从文件解析配置
   */
  async parseConfig(configPath: string): Promise<ConfigParseResult> {
    try {
      // 检查文件是否存在
      if (!(await fs.pathExists(configPath))) {
        return {
          success: false,
          errors: [
            {
              path: configPath,
              message: 'Configuration file not found',
              type: 'missing',
            },
          ],
          warnings: [],
        };
      }

      // 读取文件内容
      const content = await fs.readFile(configPath, 'utf8');

      // 解析配置
      const result = this.parseConfigFromString(content);

      this.logger.info('Configuration parsed from file', {
        configPath,
        success: result.success,
        errorCount: result.errors.length,
        warningCount: result.warnings.length,
      });

      return result;
    } catch (error) {
      this.logger.error('Failed to parse configuration file', error as Error, { configPath });

      return {
        success: false,
        errors: [
          {
            path: configPath,
            message: `Failed to read configuration file: ${error instanceof Error ? error.message : String(error)}`,
            type: 'format',
          },
        ],
        warnings: [],
      };
    }
  }

  /**
   * 从字符串解析配置
   */
  parseConfigFromString(configContent: string): ConfigParseResult {
    try {
      // 解析JSON
      const rawConfig = JSON.parse(configContent);

      // 验证配置
      return this.validateConfig(rawConfig);
    } catch (error) {
      return {
        success: false,
        errors: [
          {
            path: 'root',
            message: `Invalid JSON format: ${error instanceof Error ? error.message : String(error)}`,
            type: 'format',
          },
        ],
        warnings: [],
      };
    }
  }

  /**
   * 验证配置
   */
  validateConfig(config: unknown): ConfigParseResult {
    const errors: ConfigParseError[] = [];
    const warnings: ConfigParseWarning[] = [];

    try {
      // 使用Zod验证
      const validatedConfig = MCPClientConfigSchema.parse(config);

      // 额外的业务逻辑验证
      this.performBusinessValidation(validatedConfig, errors, warnings);

      return {
        success: errors.length === 0,
        config: validatedConfig,
        errors,
        warnings,
      };
    } catch (error) {
      if (error instanceof z.ZodError) {
        // 转换Zod错误为我们的格式
        for (const issue of error.issues) {
          errors.push({
            path: issue.path.join('.'),
            message: issue.message,
            value: 'input' in issue ? issue.input : undefined,
            type: 'validation',
          });
        }
      } else {
        errors.push({
          path: 'root',
          message: `Validation failed: ${error instanceof Error ? error.message : String(error)}`,
          type: 'validation',
        });
      }

      return {
        success: false,
        errors,
        warnings,
      };
    }
  }

  /**
   * 执行业务逻辑验证
   */
  private performBusinessValidation(
    config: MCPClientConfig,
    errors: ConfigParseError[],
    warnings: ConfigParseWarning[]
  ): void {
    // 检查服务器配置
    for (const [serverName, serverConfig] of Object.entries(config.mcpServers)) {
      // 检查命令是否为空
      if (!serverConfig.command.trim()) {
        errors.push({
          path: `mcpServers.${serverName}.command`,
          message: 'Command cannot be empty or whitespace only',
          value: serverConfig.command,
          type: 'validation',
        });
      }

      // 检查URL和transport的一致性
      if (serverConfig.url && serverConfig.transport === 'stdio') {
        warnings.push({
          path: `mcpServers.${serverName}`,
          message: 'URL specified but transport is stdio, URL will be ignored',
          suggestion: 'Set transport to "http" or "sse" to use URL',
        });
      }

      // 检查环境变量中的敏感信息
      if (serverConfig.env) {
        for (const [key, value] of Object.entries(serverConfig.env)) {
          if (key.toLowerCase().includes('token') || key.toLowerCase().includes('key')) {
            if (!value || value.trim() === '') {
              warnings.push({
                path: `mcpServers.${serverName}.env.${key}`,
                message: 'Sensitive environment variable is empty',
                suggestion: 'Consider setting this value or removing the variable',
              });
            }
          }
        }
      }
    }

    // 检查是否有重复的服务器名称（不区分大小写）
    const serverNames = Object.keys(config.mcpServers);
    const lowerCaseNames = serverNames.map(name => name.toLowerCase());
    const duplicates = lowerCaseNames.filter(
      (name, index) => lowerCaseNames.indexOf(name) !== index
    );

    if (duplicates.length > 0) {
      warnings.push({
        path: 'mcpServers',
        message: `Potential duplicate server names (case-insensitive): ${duplicates.join(', ')}`,
        suggestion: 'Use unique server names to avoid confusion',
      });
    }
  }

  /**
   * 合并配置
   */
  mergeConfigs(
    baseConfig: MCPClientConfig,
    ...configs: Partial<MCPClientConfig>[]
  ): MCPClientConfig {
    const merged: MCPClientConfig = {
      mcpServers: { ...baseConfig.mcpServers },
      globalSettings: baseConfig.globalSettings ? { ...baseConfig.globalSettings } : undefined,
    };

    for (const config of configs) {
      if (config.mcpServers) {
        Object.assign(merged.mcpServers, config.mcpServers);
      }

      if (config.globalSettings) {
        merged.globalSettings = {
          ...merged.globalSettings,
          ...config.globalSettings,
        };
      }
    }

    return merged;
  }

  /**
   * 导出配置到文件
   */
  async exportConfig(config: MCPClientConfig, outputPath: string): Promise<void> {
    try {
      // 确保输出目录存在
      await fs.ensureDir(path.dirname(outputPath));

      // 格式化并写入文件
      const configContent = JSON.stringify(config, null, 2);
      await fs.writeFile(outputPath, configContent, 'utf8');

      this.logger.info('Configuration exported successfully', { outputPath });
    } catch (error) {
      this.logger.error('Failed to export configuration', error as Error, { outputPath });
      throw error;
    }
  }

  /**
   * 获取服务器状态（简单实现）
   */
  async getServerStatus(serverName: string, _config: MCPServerConfig): Promise<MCPServerStatus> {
    // 这是一个简化的实现，实际应用中可能需要更复杂的状态检查
    return {
      name: serverName,
      status: 'unknown',
      lastCheck: new Date(),
    };
  }

  /**
   * 列出所有服务器
   */
  listServers(config: MCPClientConfig): string[] {
    return Object.keys(config.mcpServers);
  }
}
