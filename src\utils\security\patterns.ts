/**
 * 企业级安全模式定义
 * 预编译的正则表达式，防止ReDoS攻击，提供全面的安全防护
 */

import type { SecurityPatterns, ExpressionPatterns } from './types.js';

/**
 * 安全防护正则表达式模式
 * 所有模式都经过性能优化，防止ReDoS攻击
 */
export const SECURITY_PATTERNS: SecurityPatterns = {
  // 控制字符和不可见字符
  CONTROL_CHARS: /[\u0000-\u001F\u007F-\u009F]/g, // eslint-disable-line no-control-regex

  // 脚本标签（防ReDoS优化）
  SCRIPT_TAGS: /<script[^>]{0,100}>[\s\S]{0,10000}?<\/script>/gi,

  // 样式标签（防ReDoS优化）
  STYLE_TAGS: /<style[^>]{0,100}>[\s\S]{0,10000}?<\/style>/gi,

  // 事件处理器（防ReDoS优化）
  EVENT_HANDLERS: /\s*on[a-z]{1,20}\s*=\s*["'][^"']{0,1000}["']/gi,

  // 危险协议
  JAVASCRIPT_PROTOCOL: /javascript\s*:/gi,
  VBSCRIPT_PROTOCOL: /vbscript\s*:/gi,
  DATA_PROTOCOL: /data\s*:(?!image\/(?:png|jpg|jpeg|gif|webp|svg\+xml);base64,)/gi,

  // HTML实体
  HTML_ENTITIES: /&(?!(?:amp|lt|gt|quot|#39|#x27|#x2F);)[#\w]+;/gi,

  // SQL注入模式（防ReDoS优化）
  SQL_INJECTION:
    /\b(?:union|select|insert|update|delete|drop|create|alter|exec|execute|declare|cast|convert)\b/gi,

  // 命令注入模式（排除表达式中合法的逻辑运算符，防ReDoS优化）
  COMMAND_INJECTION: /[;`${}[\]\\]|(?<![&|])[&|](?![&|])/g,

  // Unicode绕过攻击
  UNICODE_BYPASS: /[\uFEFF\u200B-\u200D\u2060]/g,

  // 同形异义字攻击（西里尔字母等）
  HOMOGRAPH_ATTACK: /[\u0430-\u044F\u0410-\u042F]/g,

  // 路径遍历攻击（防ReDoS优化）
  PATH_TRAVERSAL: /(?:\.\.[/\\]|%2e%2e[%2f%5c]|%252e%252e[%252f%255c])/gi,

  // 双重编码攻击
  DOUBLE_ENCODING: /%25[0-9a-f]{2}/gi,

  // 空字节注入
  NULL_BYTE: /\0|%00|\\0/g,
};

/**
 * 表达式解析正则表达式模式
 * 用于安全的表达式解析和验证
 */
export const EXPRESSION_PATTERNS: ExpressionPatterns = {
  // 变量模式（支持嵌套属性访问）
  VARIABLE: /^\$\{([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\}$/,

  // 简单表达式模式（只允许安全字符，包括引号用于字符串字面量）
  SIMPLE_EXPRESSION: /^[a-zA-Z0-9_.${}()\s+\-*/%<>=!&|"']+$/,

  // 标识符模式
  IDENTIFIER: /^[a-zA-Z_][a-zA-Z0-9_]*$/,

  // 数字模式（支持整数和浮点数）
  NUMBER: /^-?\d+(?:\.\d+)?$/,

  // 字符串字面量模式
  STRING_LITERAL: /^["']([^"'\\]|\\.)*["']$/,

  // 布尔字面量模式
  BOOLEAN_LITERAL: /^(true|false)$/i,

  // 比较操作符模式
  COMPARISON_OPERATOR: /^(==|!=|>=|<=|>|<)$/,

  // 逻辑操作符模式
  LOGICAL_OPERATOR: /^(&&|\|\||!)$/,
};

/**
 * 危险关键字列表
 * 在表达式中不允许使用的关键字
 */
export const DANGEROUS_KEYWORDS = new Set([
  // JavaScript关键字
  'eval',
  'Function',
  'setTimeout',
  'setInterval',
  'require',
  'import',
  'process',
  'global',
  'window',
  'document',
  'console',
  '__proto__',
  'constructor',
  'prototype',
  'this',
  'with',
  'try',
  'catch',
  'throw',
  'delete',
  'new',
  'class',
  'extends',
  'super',
  'yield',
  'async',
  'await',

  // 控制流关键字
  'for',
  'while',
  'do',
  'switch',
  'case',
  'default',
  'break',
  'continue',
  'return',
  'if',
  'else',
  'var',
  'let',
  'const',

  // 系统相关
  'exec',
  'spawn',
  'child_process',
  'fs',
  'path',
  'os',
  'crypto',
  'buffer',
  'stream',
  'net',
  'http',
  'https',
  'url',
  'querystring',

  // 危险函数
  'bind',
  'call',
  'apply',
  'valueOf',
  'toString',
  'hasOwnProperty',
]);

/**
 * 允许的关键字列表
 * 在表达式中可以安全使用的关键字
 */
export const ALLOWED_KEYWORDS = new Set([
  'true',
  'false',
  'null',
  'undefined',
  'and',
  'or',
  'not',
  'eq',
  'ne',
  'gt',
  'lt',
  'gte',
  'lte',
]);

/**
 * 文件扩展名安全检查
 */
export const DANGEROUS_FILE_EXTENSIONS = new Set([
  'exe',
  'bat',
  'cmd',
  'com',
  'pif',
  'scr',
  'vbs',
  'js',
  'jar',
  'sh',
  'ps1',
  'php',
  'asp',
  'aspx',
  'jsp',
  'py',
  'rb',
  'pl',
  'cgi',
  'htaccess',
  'htpasswd',
  'ini',
  'cfg',
  'conf',
]);

/**
 * Windows保留文件名
 */
export const WINDOWS_RESERVED_NAMES = new Set([
  'CON',
  'PRN',
  'AUX',
  'NUL',
  'COM1',
  'COM2',
  'COM3',
  'COM4',
  'COM5',
  'COM6',
  'COM7',
  'COM8',
  'COM9',
  'LPT1',
  'LPT2',
  'LPT3',
  'LPT4',
  'LPT5',
  'LPT6',
  'LPT7',
  'LPT8',
  'LPT9',
]);

/**
 * 危险权限模式
 */
export const DANGEROUS_PERMISSION_PATTERNS = [
  'admin.*',
  'root.*',
  'system.*',
  'superuser.*',
  '*.execute',
  '*.delete',
  '*.modify',
  '*.write',
  '*.admin',
  '*.root',
  '*.system',
  '*.config',
];

/**
 * 私有IP地址模式
 * 用于防止SSRF攻击
 */
export const PRIVATE_IP_PATTERNS = [
  /^127\./, // 本地回环
  /^10\./, // 私有网络A类
  /^172\.(1[6-9]|2[0-9]|3[01])\./, // 私有网络B类
  /^192\.168\./, // 私有网络C类
  /^169\.254\./, // 链路本地地址
  /^::1$/, // IPv6本地回环
  /^fc00:/, // IPv6唯一本地地址
  /^fe80:/, // IPv6链路本地地址
];

/**
 * 模式缓存管理器
 * 提供正则表达式的缓存和性能优化
 */
export class PatternCache {
  private static instance: PatternCache;
  private cache = new Map<string, RegExp>();
  private maxSize = 1000;
  private accessCount = new Map<string, number>();

  private constructor() {}

  static getInstance(): PatternCache {
    if (!PatternCache.instance) {
      PatternCache.instance = new PatternCache();
    }
    return PatternCache.instance;
  }

  /**
   * 获取或创建正则表达式
   */
  getPattern(pattern: string, flags?: string): RegExp {
    const key = `${pattern}:${flags || ''}`;

    if (this.cache.has(key)) {
      this.accessCount.set(key, (this.accessCount.get(key) || 0) + 1);
      return this.cache.get(key)!;
    }

    if (this.cache.size >= this.maxSize) {
      this.evictLeastUsed();
    }

    const regex = new RegExp(pattern, flags);
    this.cache.set(key, regex);
    this.accessCount.set(key, 1);

    return regex;
  }

  /**
   * 清理最少使用的模式
   */
  private evictLeastUsed(): void {
    let leastUsedKey = '';
    let minCount = Infinity;

    for (const [key, count] of this.accessCount) {
      if (count < minCount) {
        minCount = count;
        leastUsedKey = key;
      }
    }

    if (leastUsedKey) {
      this.cache.delete(leastUsedKey);
      this.accessCount.delete(leastUsedKey);
    }
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
    this.accessCount.clear();
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): { size: number; maxSize: number; hitRate: number } {
    const totalAccess = Array.from(this.accessCount.values()).reduce(
      (sum, count) => sum + count,
      0
    );
    const hitRate = totalAccess > 0 ? (totalAccess - this.cache.size) / totalAccess : 0;

    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate,
    };
  }
}

/**
 * 便捷函数：获取缓存的正则表达式
 */
export function getCachedPattern(pattern: string, flags?: string): RegExp {
  return PatternCache.getInstance().getPattern(pattern, flags);
}

/**
 * 便捷函数：测试字符串是否匹配危险模式
 */
export function containsDangerousPattern(input: string): boolean {
  for (const pattern of Object.values(SECURITY_PATTERNS)) {
    if (pattern.test(input)) {
      return true;
    }
  }
  return false;
}

/**
 * 便捷函数：检查是否包含危险关键字
 */
export function containsDangerousKeyword(input: string): boolean {
  const words = input.toLowerCase().match(/\b\w+\b/g) || [];
  return words.some(word => DANGEROUS_KEYWORDS.has(word));
}
