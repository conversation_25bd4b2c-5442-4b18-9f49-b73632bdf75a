/**
 * MCP客户端工具注册器 - 协调所有MCP客户端相关工具的注册
 * 重构为模块化设计，符合500行限制
 */

import type { ILogger } from '../../services/logger.service.js';
import type { IMCPClientConfigService } from '../../services/mcp-client-config.service.js';
import type { IMCPClientManager } from '../../services/mcp-client-manager.service.js';
import type { IUnifiedToolRegistry } from '../unified-tool-registry.js';

import { MCPConfigToolsRegistrar } from './mcp-config-tools.js';
import { MCPClientConnectionToolsRegistrar } from './mcp-client-connection-tools.js';
import { MCPClientOperationToolsRegistrar } from './mcp-client-operation-tools.js';

export interface IMCPClientToolsRegistrar {
  registerMCPClientTools(): void;
}

export class MCPClientToolsRegistrar implements IMCPClientToolsRegistrar {
  private configToolsRegistrar: MCPConfigToolsRegistrar;
  private connectionToolsRegistrar: MCPClientConnectionToolsRegistrar;
  private operationToolsRegistrar: MCPClientOperationToolsRegistrar;

  constructor(
    private unifiedRegistry: IUnifiedToolRegistry,
    private logger: ILogger,
    private mcpClientConfigService: IMCPClientConfigService,
    private mcpClientManager: IMCPClientManager
  ) {
    // 初始化子注册器 - 注意：这些子注册器需要重构以支持统一注册表

    this.configToolsRegistrar = new MCPConfigToolsRegistrar(
      this.unifiedRegistry,
      this.logger,
      this.mcpClientConfigService
    );

    this.connectionToolsRegistrar = new MCPClientConnectionToolsRegistrar(
      this.unifiedRegistry,
      this.logger,
      this.mcpClientManager
    );

    this.operationToolsRegistrar = new MCPClientOperationToolsRegistrar(
      this.unifiedRegistry,
      this.logger,
      this.mcpClientManager
    );
  }

  /**
   * 注册所有MCP客户端工具（协调器模式）
   */
  registerMCPClientTools(): void {
    try {
      // 注册配置相关工具
      this.configToolsRegistrar.registerMCPConfigTools();

      // 注册连接相关工具
      this.connectionToolsRegistrar.registerMCPClientConnectionTools();

      // 注册操作相关工具
      this.operationToolsRegistrar.registerMCPClientOperationTools();

      this.logger.info('All MCP client tools registered successfully');
    } catch (error) {
      this.logger.error('Failed to register MCP client tools', error as Error);
      throw error;
    }
  }
}
