/**
 * 配置模板服务 - 提供配置模板生成和验证功能
 */

import * as fs from 'fs-extra';
import path from 'path';
import type { ServerConfig } from '../types/config.js';
import type { ILogger } from './logger.service.js';
import type {
  ConfigTemplate,
  ConfigValidationResult,
  ConfigValidationError,
  ConfigValidationWarning,
} from './config-template.types.js';

export class ConfigTemplateService {
  private logger: ILogger;
  private templates = new Map<string, ConfigTemplate>();

  constructor(logger: ILogger) {
    this.logger = logger.forComponent('ConfigTemplateService');
    this.initializeTemplates();
  }

  /**
   * 初始化内置配置模板
   */
  private initializeTemplates(): void {
    // 开发环境模板
    this.templates.set('development', {
      name: 'development',
      description: 'Development environment configuration',
      environment: 'development',
      config: {
        server: {
          name: 'mcp-prompt-server-dev',
          version: '2.0.0',
          description: 'MCP Prompt Server - Development',
          port: 3000,
        },
        prompts: {
          directories: ['./src/prompts', './dev-prompts'],
          watchForChanges: true,
          cacheEnabled: false, // 开发时禁用缓存
          supportedFormats: ['yaml', 'json', 'js', 'ts'],
          maxFileSize: 5 * 1024 * 1024, // 5MB for development
        },
        logging: {
          level: 'debug',
          format: 'text',
          file: './logs/dev.log',
          console: true,
        },
        plugins: {
          enabled: ['validation'],
          config: {
            validation: {
              strict: false,
              allowExtraArgs: true,
            },
          },
          autoLoad: true,
        },
        performance: {
          maxConcurrentTools: 5,
          requestTimeout: 60000, // 更长的超时时间用于调试
          cacheSize: 100,
        },
      },
    });

    // 生产环境模板
    this.templates.set('production', {
      name: 'production',
      description: 'Production environment configuration',
      environment: 'production',
      config: {
        server: {
          name: 'mcp-prompt-server',
          version: '2.0.0',
          description: 'MCP Prompt Server - Production',
        },
        prompts: {
          directories: ['./prompts'],
          watchForChanges: false, // 生产环境禁用文件监听
          cacheEnabled: true,
          supportedFormats: ['yaml', 'json'],
          maxFileSize: 1024 * 1024, // 1MB
        },
        logging: {
          level: 'info',
          format: 'json',
          file: './logs/production.log',
          console: false, // 生产环境不输出到控制台
        },
        plugins: {
          enabled: ['validation', 'cache', 'metrics'],
          config: {
            validation: {
              strict: true,
              allowExtraArgs: false,
            },
            cache: {
              ttl: 3600,
              maxSize: 10000,
            },
            metrics: {
              enabled: true,
              interval: 30000,
            },
          },
          autoLoad: true,
        },
        performance: {
          maxConcurrentTools: 20,
          requestTimeout: 30000,
          cacheSize: 5000,
        },
      },
    });

    // 测试环境模板
    this.templates.set('testing', {
      name: 'testing',
      description: 'Testing environment configuration',
      environment: 'testing',
      config: {
        server: {
          name: 'mcp-prompt-server-test',
          version: '2.0.0',
          description: 'MCP Prompt Server - Testing',
        },
        prompts: {
          directories: ['./test-prompts'],
          watchForChanges: false,
          cacheEnabled: false,
          supportedFormats: ['yaml', 'json'],
          maxFileSize: 512 * 1024, // 512KB
        },
        logging: {
          level: 'error',
          format: 'text',
          console: false,
        },
        plugins: {
          enabled: ['validation'],
          config: {
            validation: {
              strict: true,
              allowExtraArgs: false,
            },
          },
          autoLoad: false,
        },
        performance: {
          maxConcurrentTools: 3,
          requestTimeout: 5000,
          cacheSize: 50,
        },
      },
    });

    this.logger.info('Configuration templates initialized', {
      templateCount: this.templates.size,
      templates: Array.from(this.templates.keys()),
    });
  }

  /**
   * 获取配置模板
   */
  getTemplate(name: string): ConfigTemplate | undefined {
    return this.templates.get(name);
  }

  /**
   * 获取所有模板
   */
  getAllTemplates(): ConfigTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * 生成配置文件
   */
  async generateConfigFile(
    templateName: string,
    outputPath: string,
    overrides?: Partial<ServerConfig>
  ): Promise<void> {
    const template = this.getTemplate(templateName);
    if (!template) {
      throw new Error(`Template "${templateName}" not found`);
    }

    let config = { ...template.config };

    // 应用覆盖配置
    if (overrides) {
      config = this.mergeConfigs(config, overrides);
    }

    // 确保输出目录存在
    await fs.ensureDir(path.dirname(outputPath));

    // 写入配置文件
    const configContent = JSON.stringify(config, null, 2);
    await fs.outputFile(outputPath, configContent, 'utf8');

    this.logger.info('Configuration file generated', {
      template: templateName,
      outputPath,
      hasOverrides: !!overrides,
    });
  }

  /**
   * 验证配置
   */
  validateConfig(config: unknown): ConfigValidationResult {
    const errors: ConfigValidationError[] = [];
    const warnings: ConfigValidationWarning[] = [];

    if (!config || typeof config !== 'object') {
      errors.push({
        path: 'root',
        message: 'Configuration must be an object',
        value: config,
        expected: 'object',
      });
      return { valid: false, errors, warnings };
    }

    const cfg = config as Record<string, unknown>;

    // 验证必需的顶级字段
    const requiredFields = ['server', 'prompts', 'logging', 'plugins', 'performance'];
    for (const field of requiredFields) {
      if (!cfg[field]) {
        errors.push({
          path: field,
          message: `Required field "${field}" is missing`,
          expected: 'object',
        });
      }
    }

    // 验证服务器配置
    if (cfg['server'] && typeof cfg['server'] === 'object') {
      this.validateServerConfig(cfg['server'] as Record<string, unknown>, errors, warnings);
    }

    // 验证Prompt配置
    if (cfg['prompts'] && typeof cfg['prompts'] === 'object') {
      this.validatePromptConfig(cfg['prompts'] as Record<string, unknown>, errors, warnings);
    }

    // 验证日志配置
    if (cfg['logging'] && typeof cfg['logging'] === 'object') {
      this.validateLoggingConfig(cfg['logging'] as Record<string, unknown>, errors, warnings);
    }

    // 验证性能配置
    if (cfg['performance'] && typeof cfg['performance'] === 'object') {
      this.validatePerformanceConfig(
        cfg['performance'] as Record<string, unknown>,
        errors,
        warnings
      );
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 验证服务器配置
   */
  private validateServerConfig(
    server: Record<string, unknown>,
    errors: ConfigValidationError[],
    warnings: ConfigValidationWarning[]
  ): void {
    if (!server['name'] || typeof server['name'] !== 'string') {
      errors.push({
        path: 'server.name',
        message: 'Server name must be a non-empty string',
        value: server['name'],
        expected: 'string',
      });
    }

    if (!server['version'] || typeof server['version'] !== 'string') {
      errors.push({
        path: 'server.version',
        message: 'Server version must be a non-empty string',
        value: server['version'],
        expected: 'string',
      });
    }

    if (server['port'] && typeof server['port'] !== 'number') {
      errors.push({
        path: 'server.port',
        message: 'Server port must be a number',
        value: server['port'],
        expected: 'number',
      });
    } else if (
      server['port'] &&
      typeof server['port'] === 'number' &&
      (server['port'] < 1 || server['port'] > 65535)
    ) {
      warnings.push({
        path: 'server.port',
        message: 'Port should be between 1 and 65535',
        suggestion: 'Use a port in the valid range',
      });
    }
  }

  /**
   * 验证Prompt配置
   */
  private validatePromptConfig(
    prompts: Record<string, unknown>,
    errors: ConfigValidationError[],
    warnings: ConfigValidationWarning[]
  ): void {
    if (!Array.isArray(prompts['directories'])) {
      errors.push({
        path: 'prompts.directories',
        message: 'Prompt directories must be an array',
        value: prompts['directories'],
        expected: 'array',
      });
    } else if (prompts['directories'].length === 0) {
      warnings.push({
        path: 'prompts.directories',
        message: 'No prompt directories specified',
        suggestion: 'Add at least one prompt directory',
      });
    }

    if (typeof prompts['maxFileSize'] === 'number' && prompts['maxFileSize'] > 10 * 1024 * 1024) {
      warnings.push({
        path: 'prompts.maxFileSize',
        message: 'Large file size limit may impact performance',
        suggestion: 'Consider reducing the file size limit',
      });
    }
  }

  /**
   * 验证日志配置
   */
  private validateLoggingConfig(
    logging: Record<string, unknown>,
    errors: ConfigValidationError[],
    _warnings: ConfigValidationWarning[]
  ): void {
    const validLevels = ['debug', 'info', 'warn', 'error'];
    if (!validLevels.includes(logging['level'] as string)) {
      errors.push({
        path: 'logging.level',
        message: 'Invalid log level',
        value: logging['level'],
        expected: validLevels.join(' | '),
      });
    }

    const validFormats = ['json', 'text'];
    if (!validFormats.includes(logging['format'] as string)) {
      errors.push({
        path: 'logging.format',
        message: 'Invalid log format',
        value: logging['format'],
        expected: validFormats.join(' | '),
      });
    }
  }

  /**
   * 验证性能配置
   */
  private validatePerformanceConfig(
    performance: Record<string, unknown>,
    _errors: ConfigValidationError[],
    warnings: ConfigValidationWarning[]
  ): void {
    if (
      typeof performance['maxConcurrentTools'] === 'number' &&
      performance['maxConcurrentTools'] > 100
    ) {
      warnings.push({
        path: 'performance.maxConcurrentTools',
        message: 'High concurrent tool limit may impact performance',
        suggestion: 'Consider reducing the concurrent tool limit',
      });
    }

    if (typeof performance['requestTimeout'] === 'number' && performance['requestTimeout'] < 1000) {
      warnings.push({
        path: 'performance.requestTimeout',
        message: 'Very short timeout may cause request failures',
        suggestion: 'Consider increasing the timeout',
      });
    }
  }

  /**
   * 合并配置
   */
  private mergeConfigs(base: ServerConfig, override: Partial<ServerConfig>): ServerConfig {
    // 使用深度合并来避免复杂的类型问题
    return this.deepMerge(base, override);
  }

  /**
   * 深度合并两个对象
   */
  private deepMerge<T>(target: T, source: Partial<T>): T {
    const result = { ...target };

    for (const key in source) {
      const sourceValue = source[key];
      if (sourceValue !== undefined) {
        if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue)) {
          (result as Record<string, unknown>)[key as string] = this.deepMerge(
            (result as Record<string, unknown>)[key as string] || {},
            sourceValue as Record<string, unknown>
          );
        } else {
          (result as Record<string, unknown>)[key as string] = sourceValue;
        }
      }
    }

    return result;
  }

  /**
   * 创建配置备份
   */
  async createBackup(configPath: string, backupDir: string = './config/backups'): Promise<string> {
    if (!fs.existsSync(configPath)) {
      throw new Error(`Configuration file not found: ${configPath}`);
    }

    await fs.ensureDir(backupDir);

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFileName = `config-backup-${timestamp}.json`;
    const backupPath = path.join(backupDir, backupFileName);

    await fs.copy(configPath, backupPath);

    this.logger.info('Configuration backup created', {
      originalPath: configPath,
      backupPath,
    });

    return backupPath;
  }

  /**
   * 恢复配置备份
   */
  async restoreBackup(backupPath: string, targetPath: string): Promise<void> {
    if (!fs.existsSync(backupPath)) {
      throw new Error(`Backup file not found: ${backupPath}`);
    }

    // 验证备份文件
    const backupContent = await fs.readFile(backupPath, 'utf8');
    const backupConfig = JSON.parse(backupContent);
    const validation = this.validateConfig(backupConfig);

    if (!validation.valid) {
      throw new Error(
        `Invalid backup configuration: ${validation.errors.map(e => e.message).join(', ')}`
      );
    }

    await fs.copy(backupPath, targetPath);

    this.logger.info('Configuration restored from backup', {
      backupPath,
      targetPath,
    });
  }
}
