/**
 * 智能代码审查工具注册器
 * 提供代码质量分析、安全检查、最佳实践建议等功能
 */

import type { ILogger } from '../../services/logger.service.js';
import type {
  IUnifiedToolRegistry,
  ToolDefinition,
  ToolHandler,
} from '../unified-tool-registry.js';

export interface ICodeReviewToolsRegistrar {
  registerCodeReviewTools(): void;
}

export class CodeReviewToolsRegistrar implements ICodeReviewToolsRegistrar {
  private readonly REGISTRAR_NAME = 'CodeReviewTools';

  constructor(
    private unifiedRegistry: IUnifiedToolRegistry,
    private logger: ILogger
  ) {}

  /**
   * 注册所有代码审查工具
   */
  registerCodeReviewTools(): void {
    try {
      // 获取所有代码审查工具定义
      const toolDefinitions = this.getCodeReviewToolDefinitions();

      // 创建工具处理器
      const toolHandler = this.createToolHandler();

      // 注册所有工具到统一注册表
      for (const toolDefinition of toolDefinitions) {
        this.unifiedRegistry.registerTool(toolDefinition, toolHandler, this.REGISTRAR_NAME);
        this.logger.debug(`Registered Code Review tool: ${toolDefinition.name}`);
      }

      this.logger.info(`Code Review tools registered successfully. Total: ${toolDefinitions.length}`);
    } catch (error) {
      this.logger.error('Failed to register Code Review tools', error as Error);
      throw new Error(
        `Code Review tools registration failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * 获取所有代码审查工具定义
   */
  private getCodeReviewToolDefinitions(): ToolDefinition[] {
    return [
      {
        name: 'zhi___',
        description: '智能代码审查交互工具，支持预定义选项、自由文本输入和图片上传',
        inputSchema: {
          type: 'object',
          properties: {
            message: {
              type: 'string',
              description: '要显示给用户的消息',
            },
            predefined_options: {
              type: 'array',
              items: { type: 'string' },
              description: '预定义的选项列表（可选）',
            },
            is_markdown: {
              type: 'boolean',
              description: '消息是否为Markdown格式，默认为true',
              default: true,
            },
          },
          required: ['message'],
          additionalProperties: false,
        },
      },
    ];
  }

  /**
   * 创建工具处理器
   */
  private createToolHandler(): ToolHandler {
    return async (name: string, args: Record<string, unknown>) => {
      return this.handleToolCall(name, args);
    };
  }

  /**
   * 处理工具调用
   */
  private async handleToolCall(
    name: string,
    args: Record<string, unknown>
  ): Promise<{
    content: Array<{
      type: 'text';
      text: string;
    }>;
    isError?: boolean;
  }> {
    try {
      switch (name) {
        case 'zhi___':
          return this.handleCodeReviewInteraction(args);

        default:
          throw new Error(`Unknown Code Review tool: ${name}`);
      }
    } catch (error) {
      this.logger.error(`Code Review tool call failed for ${name}`, error as Error);
      throw error;
    }
  }

  /**
   * 处理智能代码审查交互
   */
  private async handleCodeReviewInteraction(args: Record<string, unknown>): Promise<{
    content: Array<{
      type: 'text';
      text: string;
    }>;
  }> {
    const {
      message,
      predefined_options,
      is_markdown = true,
    } = args as {
      message: string;
      predefined_options?: string[];
      is_markdown?: boolean;
    };

    if (!message || typeof message !== 'string') {
      throw new Error('Message parameter is required and must be a string');
    }

    try {
      // 构建交互响应
      const response = {
        success: true,
        interaction_type: 'code_review',
        message: message,
        format: is_markdown ? 'markdown' : 'plain_text',
        options: predefined_options || [],
        capabilities: [
          'text_input',
          'option_selection',
          'image_upload',
          'code_analysis',
          'security_check',
          'best_practices'
        ],
        timestamp: new Date().toISOString(),
        metadata: {
          supports_markdown: is_markdown,
          has_predefined_options: !!(predefined_options && predefined_options.length > 0),
          message_length: message.length,
        }
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(response, null, 2),
          },
        ],
      };
    } catch (error) {
      this.logger.error('Code review interaction failed', error as Error, {
        messageLength: message.length,
        hasOptions: !!(predefined_options && predefined_options.length > 0),
        isMarkdown: is_markdown,
      });
      throw new Error(
        `Code review interaction failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }
}
