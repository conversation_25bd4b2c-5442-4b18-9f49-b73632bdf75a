/**
 * 数字验证器模块
 * 专门处理数字相关的验证
 */

import type { NumberValidationOptions, SecurityEvent } from '../security/types.js';

/**
 * 数字验证器类
 * 提供企业级数字验证功能
 */
export class NumberValidator {
  private static securityEventHandlers: Array<(event: SecurityEvent) => void> = [];

  /**
   * 添加安全事件处理器
   */
  static addSecurityEventHandler(handler: (event: SecurityEvent) => void): void {
    this.securityEventHandlers.push(handler);
  }

  /**
   * 触发安全事件
   */
  private static emitSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): void {
    const fullEvent: SecurityEvent = {
      ...event,
      timestamp: new Date(),
    };

    this.securityEventHandlers.forEach(handler => {
      try {
        handler(fullEvent);
      } catch (error) {
        console.error('Security event handler failed:', error);
      }
    });
  }

  /**
   * 验证数字输入
   */
  static validateNumber(input: unknown, options: NumberValidationOptions = {}): number {
    const num = Number(input);

    if (isNaN(num) || !isFinite(num)) {
      throw new Error('Input must be a valid number');
    }

    if (options.integer && !Number.isInteger(num)) {
      throw new Error('Input must be an integer');
    }

    if (options.min !== undefined && num < options.min) {
      throw new Error(`Input must be at least ${options.min}`);
    }

    if (options.max !== undefined && num > options.max) {
      throw new Error(`Input must be no more than ${options.max}`);
    }

    return num;
  }

  /**
   * 验证端口号
   */
  static validatePort(input: unknown): number {
    return this.validateNumber(input, {
      min: 1,
      max: 65535,
      integer: true,
    });
  }

  /**
   * 验证文件大小（字节）
   */
  static validateFileSize(input: unknown, maxSize: number = 10 * 1024 * 1024): number {
    const num = Number(input);

    if (isNaN(num) || !isFinite(num)) {
      throw new Error('Input must be a valid number');
    }

    if (!Number.isInteger(num)) {
      throw new Error('Input must be an integer');
    }

    if (num < 0) {
      throw new Error('Input must be at least 0');
    }

    if (num > maxSize) {
      this.emitSecurityEvent({
        type: 'security_violation',
        message: `File size too large: ${num} bytes`,
        severity: 'medium',
      });
      throw new Error(`File size exceeds maximum`);
    }

    return num;
  }
}
