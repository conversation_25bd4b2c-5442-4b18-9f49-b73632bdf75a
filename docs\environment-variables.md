# 环境变量配置指南

本文档描述了 MCP Prompt Server 支持的所有环境变量，用于提高配置的灵活性和可移植性。

## 项目路径配置

### PROJECT_ROOT
- **描述**: 项目根目录路径
- **默认值**: 自动检测
- **示例**: `PROJECT_ROOT=/path/to/your/project`
- **用途**: 用于解析相对路径，确保跨环境兼容性

## Prompt 配置

### MCP_PROMPTS_DIRECTORIES
- **描述**: Prompt 目录列表（逗号分隔）
- **默认值**: `./dist/prompts,./dist/custom-prompts,./src/prompts,./custom-prompts,./prompts`
- **示例**: `MCP_PROMPTS_DIRECTORIES=./my-prompts,./shared-prompts`

### MCP_PROMPTS_DIRECTORY
- **描述**: 单个 Prompt 目录（向后兼容）
- **默认值**: `./prompts`
- **示例**: `MCP_PROMPTS_DIRECTORY=./my-prompts`

### PROMPTS_DIRS
- **描述**: Prompt 目录简短形式
- **默认值**: 无
- **示例**: `PROMPTS_DIRS=./prompts,./custom`

### MCP_PROMPTS_FORMATS
- **描述**: 支持的文件格式（逗号分隔）
- **默认值**: `yaml,json,js,ts,md`
- **示例**: `MCP_PROMPTS_FORMATS=md,yaml,json`

### MCP_PROMPTS_WATCH
- **描述**: 是否监听文件变化
- **默认值**: `false`
- **示例**: `MCP_PROMPTS_WATCH=true`

### MCP_PROMPTS_CACHE
- **描述**: 是否启用缓存
- **默认值**: `true`
- **示例**: `MCP_PROMPTS_CACHE=false`

### MCP_PROMPTS_MAX_FILE_SIZE
- **描述**: 最大文件大小（字节）
- **默认值**: `1048576` (1MB)
- **示例**: `MCP_PROMPTS_MAX_FILE_SIZE=2097152`

## 日志配置

### MCP_LOG_LEVEL
- **描述**: 日志级别
- **可选值**: `debug`, `info`, `warn`, `error`
- **默认值**: `info`
- **示例**: `MCP_LOG_LEVEL=debug`

### MCP_LOG_FORMAT
- **描述**: 日志格式
- **可选值**: `json`, `text`
- **默认值**: `text`
- **示例**: `MCP_LOG_FORMAT=json`

### MCP_LOG_FILE
- **描述**: 日志文件路径
- **默认值**: 无（仅控制台输出）
- **示例**: `MCP_LOG_FILE=./logs/server.log`

### MCP_LOG_CONSOLE
- **描述**: 是否输出到控制台
- **默认值**: `true`
- **示例**: `MCP_LOG_CONSOLE=false`

## 插件配置

### MCP_PLUGINS_ENABLED
- **描述**: 启用的插件列表（逗号分隔）
- **默认值**: 无
- **示例**: `MCP_PLUGINS_ENABLED=plugin1,plugin2`

### MCP_PLUGINS_AUTO_LOAD
- **描述**: 是否自动加载插件
- **默认值**: `true`
- **示例**: `MCP_PLUGINS_AUTO_LOAD=false`

## 性能配置

### MCP_PERFORMANCE_MAX_CONCURRENT_REQUESTS
- **描述**: 最大并发请求数
- **默认值**: `100`
- **示例**: `MCP_PERFORMANCE_MAX_CONCURRENT_REQUESTS=50`

### MCP_PERFORMANCE_REQUEST_TIMEOUT
- **描述**: 请求超时时间（毫秒）
- **默认值**: `30000`
- **示例**: `MCP_PERFORMANCE_REQUEST_TIMEOUT=60000`

### MCP_PERFORMANCE_MEMORY_LIMIT
- **描述**: 内存限制（MB）
- **默认值**: `512`
- **示例**: `MCP_PERFORMANCE_MEMORY_LIMIT=1024`

## 安全配置

### SECURITY_STRICT_MODE
- **描述**: 是否启用严格安全模式
- **默认值**: `false`
- **示例**: `SECURITY_STRICT_MODE=true`

### MCP_SECURITY_BLOCK_SUSPICIOUS
- **描述**: 是否阻止可疑输入
- **默认值**: `true`
- **示例**: `MCP_SECURITY_BLOCK_SUSPICIOUS=false`

### MCP_SECURITY_RATE_LIMIT
- **描述**: 是否启用速率限制
- **默认值**: `true`
- **示例**: `MCP_SECURITY_RATE_LIMIT=false`

### MCP_SECURITY_MAX_REQUESTS_PER_MINUTE
- **描述**: 每分钟最大请求数
- **默认值**: `100`
- **示例**: `MCP_SECURITY_MAX_REQUESTS_PER_MINUTE=200`

## 运行环境配置

### NODE_ENV
- **描述**: Node.js 运行环境
- **可选值**: `development`, `production`, `test`
- **默认值**: `development`
- **示例**: `NODE_ENV=production`

## 使用示例

### 开发环境配置
```bash
# .env.development
NODE_ENV=development
PROJECT_ROOT=/path/to/project
MCP_PROMPTS_DIRECTORIES=./src/prompts,./custom-prompts
MCP_PROMPTS_WATCH=true
MCP_LOG_LEVEL=debug
MCP_LOG_CONSOLE=true
```

### 生产环境配置
```bash
# .env.production
NODE_ENV=production
PROJECT_ROOT=/app
MCP_PROMPTS_DIRECTORIES=./dist/prompts,./dist/custom-prompts
MCP_PROMPTS_WATCH=false
MCP_PROMPTS_CACHE=true
MCP_LOG_LEVEL=info
MCP_LOG_FORMAT=json
MCP_LOG_FILE=./logs/production.log
SECURITY_STRICT_MODE=true
```

### Docker 环境配置
```bash
# docker-compose.yml 环境变量
PROJECT_ROOT=/app
MCP_PROMPTS_DIRECTORIES=/app/prompts,/app/custom-prompts
MCP_LOG_LEVEL=info
MCP_LOG_FORMAT=json
SECURITY_STRICT_MODE=true
```

## 配置优先级

配置的优先级从高到低为：

1. 环境变量
2. 配置文件 (config/production.json, config/default.json)
3. 默认值

## 注意事项

1. **路径配置**: 建议使用相对路径，避免硬编码绝对路径
2. **安全配置**: 生产环境建议启用严格安全模式
3. **性能配置**: 根据实际负载调整并发和超时配置
4. **日志配置**: 生产环境建议使用 JSON 格式便于日志分析
5. **环境隔离**: 不同环境使用不同的配置文件或环境变量
