/**
 * 配置模板服务类型定义
 * 从 config-template.service.ts 中拆分出来以减少文件大小
 */

import type { ServerConfig } from '../types/config.js';

export interface ConfigTemplate {
  name: string;
  description: string;
  environment: 'development' | 'production' | 'testing';
  config: ServerConfig;
}

export interface ConfigValidationResult {
  valid: boolean;
  errors: ConfigValidationError[];
  warnings: ConfigValidationWarning[];
}

export interface ConfigValidationError {
  path: string;
  message: string;
  value?: unknown;
  expected?: string;
}

export interface ConfigValidationWarning {
  path: string;
  message: string;
  suggestion?: string;
}
