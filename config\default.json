{"server": {"name": "mcp-prompt-server", "version": "2.0.0", "description": "Modern MCP Prompt Server with TypeScript", "promptsAsTools": true}, "prompts": {"directories": ["g:/wwwroot/mcp/mcp-prompt-server/dist/prompts", "g:/wwwroot/mcp/mcp-prompt-server/dist/custom-prompts", "g:/wwwroot/mcp/mcp-prompt-server/src/prompts", "g:/wwwroot/mcp/mcp-prompt-server/custom-prompts"], "watchForChanges": false, "cacheEnabled": true, "supportedFormats": ["yaml", "json", "js", "ts", "md"], "maxFileSize": 1048576}, "logging": {"level": "debug", "format": "json", "file": "./logs/server.log", "console": false}, "plugins": {"enabled": ["validation", "cache"], "config": {"cache": {"ttl": 3600, "maxSize": 1000}, "validation": {"strict": true, "allowExtraArgs": false}}, "autoLoad": true}, "performance": {"maxConcurrentTools": 10, "requestTimeout": 30000, "cacheSize": 1000}}