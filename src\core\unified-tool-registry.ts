/**
 * 统一工具注册表管理器 - 解决多个工具注册器冲突问题
 * 负责统一管理所有 MCP 工具的注册、列表和调用分发
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import {
  ListToolsRequestSchema,
  CallToolRequestSchema,
  type CallToolRequest,
} from '@modelcontextprotocol/sdk/types.js';
import type { ILogger } from '../services/logger.service.js';

/**
 * 工具定义接口 - 兼容 MCP SDK
 */
export interface ToolDefinition {
  name: string;
  description?: string;
  inputSchema: {
    type: 'object';
    properties?: Record<string, unknown>;
    required?: string[];
    additionalProperties?: boolean;
    [key: string]: unknown;
  };
}

/**
 * 工具处理器接口
 */
export interface ToolHandler {
  (
    name: string,
    args: Record<string, unknown>
  ): Promise<{
    content: Array<{
      type: 'text';
      text: string;
    }>;
    isError?: boolean;
  }>;
}

/**
 * 工具注册信息
 */
interface ToolRegistration {
  definition: ToolDefinition;
  handler: ToolHandler;
  registrarName: string;
}

/**
 * 统一工具注册表管理器接口
 */
export interface IUnifiedToolRegistry {
  initialize(): void;
  registerTool(definition: ToolDefinition, handler: ToolHandler, registrarName: string): void;
  unregisterTool(name: string): void;
  unregisterToolsByRegistrar(registrarName: string): void;
  getRegisteredTools(): ToolDefinition[];
  hasToolRegistered(name: string): boolean;
  getToolCount(): number;
  getRegistrarTools(registrarName: string): ToolDefinition[];
}

/**
 * 统一工具注册表管理器实现
 */
export class UnifiedToolRegistry implements IUnifiedToolRegistry {
  private tools = new Map<string, ToolRegistration>();
  private isInitialized = false;

  constructor(
    private mcpServer: Server,
    private logger: ILogger
  ) {}

  /**
   * 初始化统一工具注册表 - 设置 MCP 请求处理器
   */
  initialize(): void {
    if (this.isInitialized) {
      this.logger.warn('UnifiedToolRegistry is already initialized');
      return;
    }

    // 设置工具列表处理器
    this.mcpServer.setRequestHandler(ListToolsRequestSchema, async () => {
      const tools = Array.from(this.tools.values()).map(reg => reg.definition);
      this.logger.debug(`Returning ${tools.length} tools to MCP client`);
      return { tools };
    });

    // 设置工具调用处理器
    this.mcpServer.setRequestHandler(CallToolRequestSchema, async (request: CallToolRequest) => {
      const { name, arguments: args } = request.params;

      try {
        const registration = this.tools.get(name);
        if (!registration) {
          throw new Error(`Tool not found: ${name}`);
        }

        this.logger.debug(`Calling tool: ${name} from registrar: ${registration.registrarName}`);
        const result = await registration.handler(name, args || {});
        return result;
      } catch (error) {
        this.logger.error(`Tool call failed for ${name}`, error as Error);
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
          isError: true,
        };
      }
    });

    this.isInitialized = true;
    this.logger.info('UnifiedToolRegistry initialized successfully');
  }

  /**
   * 注册工具
   */
  registerTool(definition: ToolDefinition, handler: ToolHandler, registrarName: string): void {
    if (!this.isInitialized) {
      throw new Error('UnifiedToolRegistry must be initialized before registering tools');
    }

    if (this.tools.has(definition.name)) {
      this.logger.warn(`Tool ${definition.name} is already registered, overwriting`);
    }

    this.tools.set(definition.name, {
      definition,
      handler,
      registrarName,
    });

    this.logger.debug(`Registered tool: ${definition.name} from ${registrarName}`);
  }

  /**
   * 注销工具
   */
  unregisterTool(name: string): void {
    if (this.tools.delete(name)) {
      this.logger.debug(`Unregistered tool: ${name}`);
    } else {
      this.logger.warn(`Tool not found for unregistration: ${name}`);
    }
  }

  /**
   * 注销指定注册器的所有工具
   */
  unregisterToolsByRegistrar(registrarName: string): void {
    const toolsToRemove: string[] = [];

    for (const [name, registration] of this.tools.entries()) {
      if (registration.registrarName === registrarName) {
        toolsToRemove.push(name);
      }
    }

    toolsToRemove.forEach(name => this.tools.delete(name));

    if (toolsToRemove.length > 0) {
      this.logger.debug(`Unregistered ${toolsToRemove.length} tools from ${registrarName}`);
    }
  }

  /**
   * 获取所有已注册的工具定义
   */
  getRegisteredTools(): ToolDefinition[] {
    return Array.from(this.tools.values()).map(reg => reg.definition);
  }

  /**
   * 检查工具是否已注册
   */
  hasToolRegistered(name: string): boolean {
    return this.tools.has(name);
  }

  /**
   * 获取工具总数
   */
  getToolCount(): number {
    return this.tools.size;
  }

  /**
   * 获取指定注册器的工具
   */
  getRegistrarTools(registrarName: string): ToolDefinition[] {
    return Array.from(this.tools.values())
      .filter(reg => reg.registrarName === registrarName)
      .map(reg => reg.definition);
  }
}
