/**
 * 企业级安全错误处理系统
 * 提供安全的错误消息、错误分类、错误恢复等功能
 */

import { auditLogger, AuditLevel } from './audit-logger.js';
import { ProductionLogger } from './environment-parser.js';
import { getSecurityPolicy } from './config.js';

/**
 * 错误类型枚举
 */
export enum ErrorType {
  VALIDATION_ERROR = 'validation_error',
  SECURITY_ERROR = 'security_error',
  CONFIGURATION_ERROR = 'configuration_error',
  PERFORMANCE_ERROR = 'performance_error',
  SYSTEM_ERROR = 'system_error',
  NETWORK_ERROR = 'network_error',
  AUTHENTICATION_ERROR = 'authentication_error',
  AUTHORIZATION_ERROR = 'authorization_error',
}

/**
 * 错误严重性级别
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * 安全错误接口
 */
export interface SecureError {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  userMessage: string;
  code: string;
  timestamp: Date;
  context?: Record<string, unknown>;
  stack?: string;
  recoverable: boolean;
  retryable: boolean;
}

/**
 * 错误恢复策略
 */
export interface RecoveryStrategy {
  canRecover(error: SecureError): boolean;
  recover(error: SecureError): Promise<boolean>;
  getRecoveryMessage(): string;
}

/**
 * 企业级安全错误处理器
 */
export class SecureErrorHandler {
  private static instance: SecureErrorHandler;
  private errorCounter = 0;
  private recoveryStrategies: Map<ErrorType, RecoveryStrategy> = new Map();
  private errorHistory: SecureError[] = [];

  private constructor() {
    this.initializeRecoveryStrategies();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): SecureErrorHandler {
    if (!SecureErrorHandler.instance) {
      SecureErrorHandler.instance = new SecureErrorHandler();
    }
    return SecureErrorHandler.instance;
  }

  /**
   * 处理错误
   */
  async handleError(
    error: Error | SecureError,
    type?: ErrorType,
    context?: Record<string, unknown>
  ): Promise<SecureError> {
    let secureError: SecureError;

    if (this.isSecureError(error)) {
      secureError = error;
    } else {
      secureError = this.createSecureError(error, type, context);
    }

    // 记录错误
    this.logError(secureError);

    // 添加到历史记录
    this.addToHistory(secureError);

    // 尝试恢复
    if (secureError.recoverable) {
      await this.attemptRecovery(secureError);
    }

    return secureError;
  }

  /**
   * 创建安全错误对象
   */
  createSecureError(
    error: Error,
    type: ErrorType = ErrorType.SYSTEM_ERROR,
    context?: Record<string, unknown>
  ): SecureError {
    const severity = this.determineSeverity(error, type);
    const userMessage = this.generateUserMessage(error, type, severity);

    const secureError: SecureError = {
      id: this.generateErrorId(),
      type,
      severity,
      message: error.message,
      userMessage,
      code: this.generateErrorCode(type, severity),
      timestamp: new Date(),
      recoverable: this.isRecoverable(type, severity),
      retryable: this.isRetryable(type, severity),
    };

    const sanitizedContext = this.sanitizeContext(context);
    if (sanitizedContext) {
      secureError.context = sanitizedContext;
    }

    if (this.shouldIncludeStack() && error.stack) {
      secureError.stack = error.stack;
    }

    return secureError;
  }

  /**
   * 注册恢复策略
   */
  registerRecoveryStrategy(type: ErrorType, strategy: RecoveryStrategy): void {
    this.recoveryStrategies.set(type, strategy);
  }

  /**
   * 获取错误统计信息
   */
  getErrorStats(): Record<string, unknown> {
    const typeStats: Record<string, number> = {};
    const severityStats: Record<string, number> = {};

    this.errorHistory.forEach(error => {
      typeStats[error.type] = (typeStats[error.type] || 0) + 1;
      severityStats[error.severity] = (severityStats[error.severity] || 0) + 1;
    });

    return {
      totalErrors: this.errorCounter,
      recentErrors: this.errorHistory.length,
      typeDistribution: typeStats,
      severityDistribution: severityStats,
    };
  }

  /**
   * 清理错误历史
   */
  clearHistory(): void {
    this.errorHistory = [];
  }

  /**
   * 检查是否为安全错误对象
   */
  private isSecureError(error: unknown): error is SecureError {
    return (
      typeof error === 'object' &&
      error !== null &&
      'id' in error &&
      'type' in error &&
      'severity' in error
    );
  }

  /**
   * 生成错误ID
   */
  private generateErrorId(): string {
    this.errorCounter++;
    const timestamp = Date.now().toString(36);
    const counter = this.errorCounter.toString(36);
    return `err_${timestamp}_${counter}`;
  }

  /**
   * 生成错误代码
   */
  private generateErrorCode(type: ErrorType, severity: ErrorSeverity): string {
    const typeCode = type.toUpperCase().replace('_', '');
    const severityCode = severity.toUpperCase().charAt(0);
    return `${typeCode}_${severityCode}${this.errorCounter.toString().padStart(4, '0')}`;
  }

  /**
   * 确定错误严重性
   */
  private determineSeverity(error: Error, type: ErrorType): ErrorSeverity {
    // 基于错误类型的默认严重性
    const defaultSeverity: Record<ErrorType, ErrorSeverity> = {
      [ErrorType.VALIDATION_ERROR]: ErrorSeverity.LOW,
      [ErrorType.SECURITY_ERROR]: ErrorSeverity.HIGH,
      [ErrorType.CONFIGURATION_ERROR]: ErrorSeverity.MEDIUM,
      [ErrorType.PERFORMANCE_ERROR]: ErrorSeverity.MEDIUM,
      [ErrorType.SYSTEM_ERROR]: ErrorSeverity.HIGH,
      [ErrorType.NETWORK_ERROR]: ErrorSeverity.MEDIUM,
      [ErrorType.AUTHENTICATION_ERROR]: ErrorSeverity.HIGH,
      [ErrorType.AUTHORIZATION_ERROR]: ErrorSeverity.HIGH,
    };

    // 检查错误消息中的关键词来调整严重性
    const message = error.message.toLowerCase();
    if (message.includes('critical') || message.includes('fatal')) {
      return ErrorSeverity.CRITICAL;
    }
    if (message.includes('security') || message.includes('unauthorized')) {
      return ErrorSeverity.HIGH;
    }

    return defaultSeverity[type] || ErrorSeverity.MEDIUM;
  }

  /**
   * 生成用户友好的错误消息
   */
  private generateUserMessage(error: Error, type: ErrorType, _severity: ErrorSeverity): string {
    const policy = getSecurityPolicy();

    // 在生产环境中，不暴露详细的错误信息
    if (process.env['NODE_ENV'] === 'production' && !policy.allowDangerousOperations) {
      const genericMessages: Record<ErrorType, string> = {
        [ErrorType.VALIDATION_ERROR]:
          'The provided input is invalid. Please check your data and try again.',
        [ErrorType.SECURITY_ERROR]:
          'A security issue was detected. Please contact support if this persists.',
        [ErrorType.CONFIGURATION_ERROR]:
          'A configuration issue was detected. Please contact your administrator.',
        [ErrorType.PERFORMANCE_ERROR]:
          'The operation is taking longer than expected. Please try again later.',
        [ErrorType.SYSTEM_ERROR]: 'An internal error occurred. Please try again later.',
        [ErrorType.NETWORK_ERROR]:
          'A network error occurred. Please check your connection and try again.',
        [ErrorType.AUTHENTICATION_ERROR]: 'Authentication failed. Please verify your credentials.',
        [ErrorType.AUTHORIZATION_ERROR]: 'You do not have permission to perform this action.',
      };

      return genericMessages[type] || 'An unexpected error occurred. Please try again later.';
    }

    // 在开发环境中，提供更详细的信息
    return this.sanitizeErrorMessage(error.message);
  }

  /**
   * 清理错误消息中的敏感信息
   */
  private sanitizeErrorMessage(message: string): string {
    return message
      .replace(/\/[^\s]+/g, '[PATH]')
      .replace(/\\[^\s]+/g, '[PATH]')
      .replace(/password[=:]\s*\S+/gi, 'password=[HIDDEN]')
      .replace(/token[=:]\s*\S+/gi, 'token=[HIDDEN]')
      .replace(/key[=:]\s*\S+/gi, 'key=[HIDDEN]')
      .replace(/secret[=:]\s*\S+/gi, 'secret=[HIDDEN]');
  }

  /**
   * 清理上下文信息
   */
  private sanitizeContext(context?: Record<string, unknown>): Record<string, unknown> | undefined {
    if (!context) {
      return undefined;
    }

    const sanitized: Record<string, unknown> = {};
    for (const [key, value] of Object.entries(context)) {
      if (
        ['password', 'token', 'key', 'secret'].some(sensitive =>
          key.toLowerCase().includes(sensitive)
        )
      ) {
        sanitized[key] = '[HIDDEN]';
      } else if (typeof value === 'string') {
        sanitized[key] = this.sanitizeErrorMessage(value);
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /**
   * 检查是否应该包含堆栈跟踪
   */
  private shouldIncludeStack(): boolean {
    return process.env['NODE_ENV'] !== 'production' || getSecurityPolicy().allowDangerousOperations;
  }

  /**
   * 检查错误是否可恢复
   */
  private isRecoverable(type: ErrorType, severity: ErrorSeverity): boolean {
    if (severity === ErrorSeverity.CRITICAL) {
      return false;
    }

    const recoverableTypes = [
      ErrorType.VALIDATION_ERROR,
      ErrorType.PERFORMANCE_ERROR,
      ErrorType.NETWORK_ERROR,
    ];

    return recoverableTypes.includes(type);
  }

  /**
   * 检查错误是否可重试
   */
  private isRetryable(type: ErrorType, severity: ErrorSeverity): boolean {
    if (severity === ErrorSeverity.CRITICAL) {
      return false;
    }

    const retryableTypes = [
      ErrorType.PERFORMANCE_ERROR,
      ErrorType.NETWORK_ERROR,
      ErrorType.SYSTEM_ERROR,
    ];

    return retryableTypes.includes(type);
  }

  /**
   * 记录错误
   */
  private logError(error: SecureError): void {
    auditLogger.logEvent({
      level: this.mapSeverityToAuditLevel(error.severity),
      category: 'error',
      action: 'error_occurred',
      details: {
        errorId: error.id,
        errorType: error.type,
        errorCode: error.code,
        message: error.message,
        userMessage: error.userMessage,
        context: error.context,
        recoverable: error.recoverable,
        retryable: error.retryable,
      },
      risk:
        error.severity === 'critical' ? 'critical' : error.severity === 'high' ? 'high' : 'medium',
    });
  }

  /**
   * 映射错误严重性到审计级别
   */
  private mapSeverityToAuditLevel(severity: ErrorSeverity): AuditLevel {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
        return AuditLevel.CRITICAL;
      case ErrorSeverity.HIGH:
        return AuditLevel.ERROR;
      case ErrorSeverity.MEDIUM:
        return AuditLevel.WARN;
      case ErrorSeverity.LOW:
      default:
        return AuditLevel.INFO;
    }
  }

  /**
   * 添加到错误历史
   */
  private addToHistory(error: SecureError): void {
    this.errorHistory.push(error);

    // 限制历史记录大小
    if (this.errorHistory.length > 1000) {
      this.errorHistory = this.errorHistory.slice(-500);
    }
  }

  /**
   * 尝试错误恢复
   */
  private async attemptRecovery(error: SecureError): Promise<void> {
    const strategy = this.recoveryStrategies.get(error.type);
    if (!strategy || !strategy.canRecover(error)) {
      return;
    }

    try {
      const recovered = await strategy.recover(error);
      if (recovered) {
        ProductionLogger.log(`Error ${error.id} recovered successfully`);
        auditLogger.logEvent({
          level: AuditLevel.INFO,
          category: 'recovery',
          action: 'error_recovered',
          details: {
            errorId: error.id,
            recoveryMessage: strategy.getRecoveryMessage(),
          },
          risk: 'low',
        });
      }
    } catch (recoveryError) {
      ProductionLogger.error(`Failed to recover from error ${error.id}:`, recoveryError);
    }
  }

  /**
   * 初始化恢复策略
   */
  private initializeRecoveryStrategies(): void {
    // 这里可以注册默认的恢复策略
    // 例如：网络错误重试、配置重新加载等
  }
}

// 导出全局错误处理器实例
export const secureErrorHandler = SecureErrorHandler.getInstance();

// 便捷函数
export async function handleError(
  error: Error,
  type?: ErrorType,
  context?: Record<string, unknown>
): Promise<SecureError> {
  return secureErrorHandler.handleError(error, type, context);
}

export function createSecureError(
  error: Error,
  type: ErrorType = ErrorType.SYSTEM_ERROR,
  context?: Record<string, unknown>
): SecureError {
  return secureErrorHandler.createSecureError(error, type, context);
}
